<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class ChildFamilyTreeNodeJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Use profile data when available, fallback to legacy fields
        $profile = $this->profile;

        return [
            "id" => $this->id,
            "profile_id" => $this->profile_id,
            'name' => $this->getDisplayName(),
            'gender' => $profile ? $profile->gender : $this->gender,
            'alive' => $this->alive,
        ];
    }
}
