<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class ProfileJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'middle_name' => $this->middle_name,
            'last_name' => $this->last_name,
            'grandfather_name' => $this->grandfather_name,
            'nick_name' => $this->nick_name,
            'display_name' => $this->display_name,
            'full_name' => $this->full_name,
            'gender' => $this->gender,
            'birth_date' => $this->birth_date?->format('Y-m-d'),
            'death_date' => $this->death_date?->format('Y-m-d'),
            'birth_place' => $this->birth_place,
            'death_place' => $this->death_place,
            'overview' => $this->overview,
            'image' => $this->image_url,
            'thumb_image_url' => $this->thumb_image_url,
            'cover_image' => $this->cover_image_url,
            'address' => $this->address,
            'job' => $this->job,
            'facebook_link' => $this->facebook_link,
            'x_link' => $this->x_link,
            'snapshot_link' => $this->snapshot_link,
            'youtube_link' => $this->youtube_link,
            'linkedin_link' => $this->linkedin_link,
            'instagram_link' => $this->instagram_link,
            'social_links' => $this->social_links,
            'is_alive' => $this->isAlive(),
            'has_living_member' => $this->hasLivingMember(),
            'average_rating' => $this->average_rating,
            'reviews_count' => $this->reviews_count,
            'review_stats' => $this->review_stats,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
