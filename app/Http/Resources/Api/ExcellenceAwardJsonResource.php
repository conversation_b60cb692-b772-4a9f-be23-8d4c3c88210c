<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class ExcellenceAwardJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id" => $this->id,
            "title" => $this->title,
            "content" => $this->content,
            "type" => $this->type?->getLabel() ?? __('excellence_award.types.academic_excellence'),
            "type_key" => $this->type?->value ?? 'academic_excellence',
            "published_at" => $this->published_at->translatedFormat('j F Y h:i'),
            "created_at" => $this->created_at->format('Y-m-d H:i'),
            "share_link" => $this->share_link,
            "visits_count" => $this->visits_count,
        ];
    }
}
