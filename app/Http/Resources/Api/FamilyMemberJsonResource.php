<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class FamilyMemberJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Use profile data when available, fallback to legacy fields for backward compatibility
        $profile = $this->profile;

        return [
            "id" => $this->id,
            "profile_id" => $this->profile_id,
            // Name fields - use profile data when available
            "first_name" => $profile ? $profile->first_name : $this->first_name,
            "middle_name" => $profile ? $profile->middle_name : $this->middle_name,
            "last_name" => $profile ? $profile->last_name : $this->last_name,
            "grandfather_name" => $profile ? $profile->grandfather_name : $this->grandfather_name,
            "branch" => $this->branch?->name,
            "branch_id" => $this->branch_id,
            // Basic info - use profile data when available
            "gender" => $profile ? $profile->gender : $this->gender,
            "mobile" => $this->mobile,
            "email" => $this->email,
            // Images - use profile data when available
            "image" => $this->thumbImageUrl,
            // Dates and places - use profile data when available
            'birth_date' => $profile ? $profile->birth_date?->format('Y-m-d') : $this->birth_date?->format('Y-m-d'),
            'birth_place' => $profile ? $profile->birth_place : $this->birth_place,
            'country' => $this->country?->name,
            'country_id' => $this->country_id,
            'city' => $this->city?->name,
            'city_id' => $this->city_id,
            // Address and overview - use profile data when available
            'address' => $profile ? $profile->address : $this->address,
            'overview' => $profile ? $profile->overview : $this->overview,
            'cover_image' => $profile ? $profile->cover_image : $this->cover_image,
            // Social links - use profile data when available
            'facebook_link' => $profile ? $profile->facebook_link : $this->facebook_link,
            'x_link' => $profile ? $profile->x_link : $this->x_link,
            'snapshot_link' => $profile ? $profile->snapshot_link : $this->snapshot_link,
            'youtube_link' => $profile ? $profile->youtube_link : $this->youtube_link,
            'linkedin_link' => $profile ? $profile->linkedin_link : $this->linkedin_link,
            'instagram_link' => $profile ? $profile->instagram_link : $this->instagram_link,
            // User-specific fields (remain with FamilyMember)
            'cv_file' => $this->cv_file,
            'cv_text' => $this->cv_text,
            'cv_type' => $this->cv_type,
            'family_tree_node_id' => $this->family_tree_node_id,
        ];
    }
}
