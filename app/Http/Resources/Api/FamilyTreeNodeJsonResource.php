<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class FamilyTreeNodeJsonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Use profile data when available, fallback to legacy fields and family member data
        $profile = $this->profile;
        $familyMember = $this->family_member;

        return [
            "id" => $this->id,
            "profile_id" => $this->profile_id,
            // Name - use display name logic (prioritizes name field, falls back to profile)
            'name' => $this->name,

            'profile_display_name' => $this->profile?->display_name,
            'nick_name' => $profile ? $profile->nick_name : $this->nick_name,
            // Overview - priority: profile > family_member > legacy
            'overview' => $profile ? $profile->overview : ($familyMember ? $familyMember->overview : $this->overview),
            // Images - priority: profile > family_member > legacy
            'image' => $profile ? $profile->image_url : ($familyMember ? $familyMember->image_url : $this->imageUrl),
            'thumb_image_url' => $profile ? $profile->thumb_image_url : ($familyMember ? $familyMember->thumb_image_url : $this->thumb_image_url),
            // Tree structure fields
            'order' => $this->order,
            'alive' => $this->alive,
            'family_member_id' => $this->family_member_id,
            // Basic info - use profile data when available
            'gender' => $profile ? $profile->gender : $this->gender,
            'birth_date' => $profile ? $profile->birth_date : $this->birth_date,
            'death_date' => $profile ? $profile->death_date : $this->death_date,
            'birth_place' => $profile ? $profile->birth_place : $this->birth_place,
            'death_place' => $profile ? $profile->death_place : $this->death_place,
            'job' => $profile ? $profile->job : $this->job,
            'address' => $profile ? $profile->address : $this->address,
            // Tree relationships
            'children' => ChildFamilyTreeNodeJsonResource::collection($this->children),
            'parent' => new ChildFamilyTreeNodeJsonResource($this->parent),
        ];
    }
}
