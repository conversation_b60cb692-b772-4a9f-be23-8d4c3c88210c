<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AchievementResource;
use App\Http\Resources\Api\Collections\OccasionCollection;
use App\Http\Resources\Api\FamilyMemberJsonResource;
use App\Http\Resources\Api\OccasionJsonResource;
use App\Http\Resources\Api\FamilyMemberExperienceResource;
use App\Http\Resources\Api\Skill;
use App\Http\Resources\Api\SkillResource;
use App\Models\Achievement;
use App\Models\FamilyMember;
use App\Models\Occasion;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    /**
     * Get profile
     * @authenticated
     */
    public function profile(Request $request)
    {
        $id = $request->id;
        $validator = Validator::make(
            $request->all(),
            [
                // id. only provide if you want to return specefic user Example: 1
                "id" => "nullable",
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        if ($id) {
            $user = FamilyMember::with('profile')->find($id);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load('profile');
            }
        }
        return response()->json(
            new FamilyMemberJsonResource($user),
            200
        );
    }
    /**
     * Update profile
     * @authenticated
     */
    public function updateProfile(Request $request)
    {

        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                // first_name. Example: Ayman
                "first_name" => 'required',
                // middle_name. Example: Ameen
                "middle_name" => "required",
                // last name. Example: Shaef
                "last_name" => "required",
                // grandfather_name. Example: Naji
                "grandfather_name" => "nullable",
                // branch_id. Example: 1
                "branch_id" => "required",
                // gender. 1 for male, 2 for female. Example: 1
                "gender" => "required|in:1,2",
                // mobile. Example: 777582069
                "mobile" => 'required',
                // email. Example: <EMAIL>
                'email' => 'nullable|email|unique:family_members,email,' . $request->user()->id,
                //  image file
                'image' => 'nullable|image|mimes:jpeg,png,jpg',
                //  birth_date. Example: 2500
                'birth_date' => 'nullable',
                //  birth_place. Example: 2500
                'birth_place' => 'nullable',
                //  country_id. Example: 2500
                'country_id' => 'nullable',
                //  city_id. Example: 2500
                'city_id' => 'nullable',
                //  address. Example: 2500
                'address' => 'nullable',
                //  overview.
                'overview' => 'nullable',
                //  cover_image.
                'cover_image' => 'nullable',
                //  facebook_link.
                'facebook_link' => 'nullable',
                //  x_link.
                'x_link' => 'nullable',
                //  snapshot_link.
                'snapshot_link' => 'nullable',
                //  youtube_link.
                'youtube_link' => 'nullable',
                //  linkedin_link.
                'linkedin_link' => 'nullable',
                //  instagram_link.
                'instagram_link' => 'nullable',
            ],
            [],
            [
                "first_name" => __('family_member.fields.first_name.label'),
                "middle_name" => __('family_member.fields.middle_name.label'),
                "last_name" => __('family_member.fields.last_name.label'),
                "grandfather_name" => __('family_member.fields.grandfather_name.label'),
                "mobile" => __('family_member.fields.mobile.label'),
                "gender" => __('family_member.fields.gender.label'),
                "branch_id" => __('family_member.fields.branch_id.label'),
                "email" => __('family_member.fields.email.label'),
                "image" => __('family_member.fields.image.label'),
                "birth_date" => __('family_member.fields.birth_date.label'),
                "birth_place" => __('family_member.fields.birth_place.label'),
                "country_id" => __('family_member.fields.country_id.label'),
                "city_id" => __('family_member.fields.city_id.label'),
                "address" => __('family_member.fields.address.label'),
                "overview" => __('family_member.fields.overview.label'),
                "cover_image" => __('family_member.fields.cover_image.label'),
                "facebook_link" => __('family_member.fields.facebook_link.label'),
                "x_link" => __('family_member.fields.x_link.label'),
                "snapshot_link" => __('family_member.fields.snapshot_link.label'),
                "youtube_link" => __('family_member.fields.youtube_link.label'),
                "linkedin_link" => __('family_member.fields.linkedin_link.label'),
                "instagram_link" => __('family_member.fields.instagram_link.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $data = $request->user();
        $file = $request->file('image');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members', $fileName, 'uploads');
            $inputs['image'] = "family_members/" . $fileName;
        }
        $coverImage = $request->file('cover_image');
        if ($coverImage) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $coverImage->getClientOriginalExtension();
            $coverImage->storeAs('family_members/covers', $fileName, 'uploads');
            $inputs['cover_image'] = "family_members/covers/" . $fileName;
        }
        $data->profile()->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.profile_updated'),
            'data' => new FamilyMemberJsonResource($data),
        ]);
    }
    /**
     * Update CV
     *
     */
    public function updateCV(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'cv_type' => 'required|in:image,pdf,text',
                'cv_file' => 'required_if:cv_type,image,pdf|file|mimes:jpeg,png,jpg,pdf',
                'cv_text' => 'required_if:cv_type,text',
            ],
            [],
            [
                'cv_type' => __('family_member.fields.cv_type.label'),
                'cv_file' => __('family_member.fields.cv_file.label'),
                'cv_text' => __('family_member.fields.cv_text.label'),
            ]

        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $data = $request->user();
        $file = $request->file('cv_file');
        if ($file && in_array($request->cv_type, ['image', 'pdf'])) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/cvs', $fileName, 'uploads');
            $inputs['cv_file'] = "family_members/cvs/" . $fileName;
        } else {
            $inputs['cv_text'] = $request->cv_text;
        }
        $data->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.cv_updated'),
            'cv_type' => $data->cv_type,
            'cv_text' => $data->cv_text,
            'cv_file' => $data->cv_file,
        ]);
    }
    /**
     * Update Profile Image
     *
     */
    public function updateProfileImage(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'image' => 'required|image',
            ],
            [],
            [
                'image' => __('family_member.fields.image.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('image');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members', $fileName, 'uploads');
            $inputs['image'] = "family_members/" . $fileName;
        }
        $request->user()->update([
            'image' => $inputs['image'],
        ]);
        return response()->json([
            'message' => __('family_member.messages.image_updated'),
            'data' => [
                'image' => $request->user()->image
            ],
        ]);
    }
    /**
     * Update Profile Cover
     *
     */
    public function updateProfileCover(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'cover_image' => 'required|image',
            ],
            [],
            [
                'cover_image' => __('family_member.fields.cover_image.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('cover_image');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/covers', $fileName, 'uploads');
            $inputs['cover_image'] = "family_members/covers/" . $fileName;
        }
        $request->user()->update([
            'cover_image' => $inputs['cover_image'],
        ]);
        return response()->json([
            'message' => __('family_member.messages.cover_image_updated'),
            'cover_image' => $request->user()->cover_image
        ]);
    }

    /**
     * Get Experiences
     *
     */
    public function getExperiences(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'nullable',
            ],

        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $familyMemberId = $request->id;
        if ($familyMemberId) {
            $user = FamilyMember::with(['profile', 'experiences'])->find($familyMemberId);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load(['profile', 'experiences']);
            }
        }
        return response()->json(
            FamilyMemberExperienceResource::collection($user->experiences),
        );
    }

    /**
     * Create Experience
     *
     */
    public function createExperience(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'company_name' => 'required',
                'job_title' => 'required',
                'location' => 'required',
                'start_date' => 'required|date:Y-m-d',
                'end_date' => 'nullable|date:Y-m-d',
                'description' => 'required',
                'type' => 'required|in:job,training,volunteering',
            ],
            [],
            [
                'company_name' => __('Company name'),
                'job_title' => __('Job title'),
                'location' => __('Location'),
                'start_date' => __('Start date'),
                'end_date' => __('End date'),
                'description' => __('Description'),
                'type' => __('Type'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->experiences()->create($inputs);
        return response()->json([
            'message' => __('family_member.messages.experience_created'),
        ]);
    }

    /**
     * Update Experience
     *
     */
    public function updateExperience(Request $request, $id)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'company_name' => 'required',
                'job_title' => 'required',
                'location' => 'required',
                'start_date' => 'required|date:Y-m-d',
                'end_date' => 'nullable|date:Y-m-d',
                'description' => 'required',
                'type' => 'required|in:job,training,volunteering',
            ],
            [],
            [
                'company_name' => __('Company name'),
                'job_title' => __('Job title'),
                'location' => __('Location'),
                'start_date' => __('Start date'),
                'end_date' => __('End date'),
                'description' => __('Description'),
                'type' => __('Type'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->experiences()->where('id', $id)->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.experience_updated'),
            'data' => new FamilyMemberExperienceResource($request->user()->experiences()->where('id', $id)->first()),
        ]);
    }

    /**
     * Delete Experience
     */
    public function deleteExperience(Request $request, $id)
    {
        $request->user()->experiences()->where('id', $id)->delete();
        return response()->json([
            'message' => __('family_member.messages.experience_deleted'),
        ]);
    }

    public function getSkills(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'nullable',
            ],
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $familyMemberId = $request->id;
        if ($familyMemberId) {
            $user = FamilyMember::with(['profile', 'skills'])->find($familyMemberId);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load(['profile', 'skills']);
            }
        }
        return response()->json(
            SkillResource::collection($user->skills)
        );
    }

    public function createSkill(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required',
                'description' => 'required',
                'level' => 'required|numeric|min:0|max:100',
            ],
            [],
            [
                'name' => __('Name'),
                'description' => __('Description'),
                'level' => __('Level'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->skills()->create($inputs);
        return response()->json([
            'message' => __('family_member.messages.skill_created'),
        ]);
    }

    public function updateSkill(Request $request, $id)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required',
                'description' => 'required',
                'level' => 'required|numeric|min:0|max:100',
            ],
            [],
            [
                'name' => __('Name'),
                'description' => __('Description'),
                'level' => __('Level'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->skills()->where('id', $id)->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.skill_updated'),
        ]);
    }

    public function deleteSkill(Request $request, $id)
    {
        $request->user()->skills()->where('id', $id)->delete();
        return response()->json([
            'message' => __('family_member.messages.skill_deleted'),
        ]);
    }

    public function getAchievements(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'nullable',
            ],
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $familyMemberId = $request->id;
        if ($familyMemberId) {
            $user = FamilyMember::with(['profile', 'achievements'])->find($familyMemberId);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load(['profile', 'achievements']);
            }
        }
        return response()->json(
            AchievementResource::collection($user->achievements)
        );
    }

    public function createAchievement(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'title' => 'required',
                'description' => 'required',
                'file' => 'required|file',
                'date' => 'required|date',
                'type' => 'required|in:certificate,award,personal_achievement',
            ],
            [],
            [
                'title' => __('Title'),
                'description' => __('Description'),
                'file' => __('File'),
                'date' => __('Date'),
                'type' => __('Type'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('file');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/achievements', $fileName, 'uploads');
            $inputs['file'] = "family_members/achievements/" . $fileName;
        }
        $request->user()->achievements()->create($inputs);
        return response()->json([
            'message' => __('family_member.messages.achievement_created'),
        ]);
    }

    public function updateAchievement(Request $request, $id)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'title' => 'required',
                'description' => 'required',
                'file' => 'nullable|file',
                'date' => 'required|date',
                'type' => 'required|in:certificate,award,personal_achievement',
            ],
            [],
            [
                'title' => __('Title'),
                'description' => __('Description'),
                'file' => __('File'),
                'date' => __('Date'),
                'type' => __('Type'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('file');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/achievements', $fileName, 'uploads');
            $inputs['file'] = "family_members/achievements/" . $fileName;
        }
        $request->user()->achievements()->where('id', $id)->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.achievement_updated'),
        ]);
    }

    public function deleteAchievement(Request $request, $id)
    {
        $request->user()->achievements()->where('id', $id)->delete();
        return response()->json([
            'message' => __('family_member.messages.achievement_deleted'),
        ]);
    }
}
