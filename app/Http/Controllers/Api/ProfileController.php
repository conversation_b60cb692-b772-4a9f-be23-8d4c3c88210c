<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AchievementResource;
use App\Http\Resources\Api\Collections\OccasionCollection;
use App\Http\Resources\Api\FamilyMemberJsonResource;
use App\Http\Resources\Api\OccasionJsonResource;
use App\Http\Resources\Api\FamilyMemberExperienceResource;
use App\Http\Resources\Api\Skill;
use App\Http\Resources\Api\SkillResource;
use App\Models\Achievement;
use App\Models\FamilyMember;
use App\Models\Occasion;
use Carbon\Carbon;
use Google\Cloud\Iam\V1\AuditLogConfig\LogType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    /**
     * Get profile
     * @authenticated
     */
    public function profile(Request $request)
    {
        $id = $request->id;
        $validator = Validator::make(
            $request->all(),
            [
                // id. only provide if you want to return specefic user Example: 1
                "id" => "nullable",
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        if ($id) {
            // Load specific user with profile and related data
            $user = FamilyMember::with([
                'profile',
                'branch',
                'country',
                'city'
            ])->find($id);

            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            // Load authenticated user with profile and related data
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load([
                    'profile',
                    'branch',
                    'country',
                    'city'
                ]);
            }
        }

        return response()->json(
            new FamilyMemberJsonResource($user),
            200
        );
    }
    /**
     * Update profile
     * @authenticated
     */
    public function updateProfile(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                // first_name. Example: Ayman
                "first_name" => 'required',
                // middle_name. Example: Ameen
                "middle_name" => "required",
                // last name. Example: Shaef
                "last_name" => "required",
                // grandfather_name. Example: Naji
                "grandfather_name" => "nullable",
                // branch_id. Example: 1
                "branch_id" => "required",
                // gender. 1 for male, 2 for female. Example: 1
                "gender" => "required|in:1,2",
                // mobile. Example: 777582069
                "mobile" => 'required',
                // email. Example: <EMAIL>
                'email' => 'nullable|email|unique:family_members,email,' . $request->user()->id,
                //  image file
                'image' => 'nullable|image|mimes:jpeg,png,jpg',
                //  birth_date. Example: 2500
                'birth_date' => 'nullable',
                //  birth_place. Example: 2500
                'birth_place' => 'nullable',
                //  country_id. Example: 2500
                'country_id' => 'nullable',
                //  city_id. Example: 2500
                'city_id' => 'nullable',
                //  address. Example: 2500
                'address' => 'nullable',
                //  overview.
                'overview' => 'nullable',
                //  cover_image.
                'cover_image' => 'nullable',
                //  facebook_link.
                'facebook_link' => 'nullable',
                //  x_link.
                'x_link' => 'nullable',
                //  snapshot_link.
                'snapshot_link' => 'nullable',
                //  youtube_link.
                'youtube_link' => 'nullable',
                //  linkedin_link.
                'linkedin_link' => 'nullable',
                //  instagram_link.
                'instagram_link' => 'nullable',
            ],
            [],
            [
                "first_name" => __('family_member.fields.first_name.label'),
                "middle_name" => __('family_member.fields.middle_name.label'),
                "last_name" => __('family_member.fields.last_name.label'),
                "grandfather_name" => __('family_member.fields.grandfather_name.label'),
                "mobile" => __('family_member.fields.mobile.label'),
                "gender" => __('family_member.fields.gender.label'),
                "branch_id" => __('family_member.fields.branch_id.label'),
                "email" => __('family_member.fields.email.label'),
                "image" => __('family_member.fields.image.label'),
                "birth_date" => __('family_member.fields.birth_date.label'),
                "birth_place" => __('family_member.fields.birth_place.label'),
                "country_id" => __('family_member.fields.country_id.label'),
                "city_id" => __('family_member.fields.city_id.label'),
                "address" => __('family_member.fields.address.label'),
                "overview" => __('family_member.fields.overview.label'),
                "cover_image" => __('family_member.fields.cover_image.label'),
                "facebook_link" => __('family_member.fields.facebook_link.label'),
                "x_link" => __('family_member.fields.x_link.label'),
                "snapshot_link" => __('family_member.fields.snapshot_link.label'),
                "youtube_link" => __('family_member.fields.youtube_link.label'),
                "linkedin_link" => __('family_member.fields.linkedin_link.label'),
                "instagram_link" => __('family_member.fields.instagram_link.label'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $user->load('profile');

        // Separate profile data from family member data
        $profileData = [];
        $familyMemberData = [];

        // Profile fields
        $profileFields = [
            'first_name',
            'middle_name',
            'last_name',
            'grandfather_name',
            'gender',
            'birth_date',
            'birth_place',
            'address',
            'overview',
            'image',
            'cover_image',
            'facebook_link',
            'x_link',
            'snapshot_link',
            'youtube_link',
            'linkedin_link',
            'instagram_link'
        ];

        // Family member fields
        $familyMemberFields = ['branch_id', 'mobile', 'email', 'country_id', 'city_id'];

        foreach ($inputs as $key => $value) {
            if (in_array($key, $profileFields)) {
                $profileData[$key] = $value;
            } elseif (in_array($key, $familyMemberFields)) {
                $familyMemberData[$key] = $value;
            }
        }

        // Handle image upload for profile
        $file = $request->file('image');
        if ($file) {
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members', $fileName, 'uploads');
            $profileData['image'] = "family_members/" . $fileName;
        }

        // Handle cover image upload for profile
        $coverImage = $request->file('cover_image');
        if ($coverImage) {
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $coverImage->getClientOriginalExtension();
            $coverImage->storeAs('family_members/covers', $fileName, 'uploads');
            $profileData['cover_image'] = "family_members/covers/" . $fileName;
        }

        // Handle social links as JSON for profile
        if (
            isset($profileData['facebook_link']) || isset($profileData['x_link']) ||
            isset($profileData['snapshot_link']) || isset($profileData['youtube_link']) ||
            isset($profileData['linkedin_link']) || isset($profileData['instagram_link'])
        ) {

            $socialLinks = $user->profile ? $user->profile->social_links ?? [] : [];

            if (isset($profileData['facebook_link'])) {
                $socialLinks['facebook'] = $profileData['facebook_link'];
                unset($profileData['facebook_link']);
            }
            if (isset($profileData['x_link'])) {
                $socialLinks['x'] = $profileData['x_link'];
                unset($profileData['x_link']);
            }
            if (isset($profileData['snapshot_link'])) {
                $socialLinks['snapshot'] = $profileData['snapshot_link'];
                unset($profileData['snapshot_link']);
            }
            if (isset($profileData['youtube_link'])) {
                $socialLinks['youtube'] = $profileData['youtube_link'];
                unset($profileData['youtube_link']);
            }
            if (isset($profileData['linkedin_link'])) {
                $socialLinks['linkedin'] = $profileData['linkedin_link'];
                unset($profileData['linkedin_link']);
            }
            if (isset($profileData['instagram_link'])) {
                $socialLinks['instagram'] = $profileData['instagram_link'];
                unset($profileData['instagram_link']);
            }

            $profileData['social_links'] = $socialLinks;
        }

        // Update or create profile
        if ($user->profile) {
            $user->profile->update($profileData);
        } else {
            $profile = $user->profile()->create($profileData);
            $user->update(['profile_id' => $profile->id]);
        }

        // Update family member data
        if (!empty($familyMemberData)) {
            $user->update($familyMemberData);
        }

        // Reload relationships for response
        $user->load(['profile', 'branch', 'country', 'city']);

        return response()->json([
            'message' => __('family_member.messages.profile_updated'),
            'data' => new FamilyMemberJsonResource($user),
        ]);
    }
    /**
     * Update CV
     *
     */
    public function updateCV(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'cv_type' => 'required|in:image,pdf,text',
                'cv_file' => 'required_if:cv_type,image,pdf|file|mimes:jpeg,png,jpg,pdf',
                'cv_text' => 'required_if:cv_type,text',
            ],
            [],
            [
                'cv_type' => __('family_member.fields.cv_type.label'),
                'cv_file' => __('family_member.fields.cv_file.label'),
                'cv_text' => __('family_member.fields.cv_text.label'),
            ]

        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $data = $request->user();
        $file = $request->file('cv_file');
        if ($file && in_array($request->cv_type, ['image', 'pdf'])) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/cvs', $fileName, 'uploads');
            $inputs['cv_file'] = "family_members/cvs/" . $fileName;
        } else {
            $inputs['cv_text'] = $request->cv_text;
        }
        $data->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.cv_updated'),
            'cv_type' => $data->cv_type,
            'cv_text' => $data->cv_text,
            'cv_file' => $data->cv_file,
        ]);
    }
    /**
     * Update Profile Image
     *
     */
    public function updateProfileImage(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'image' => 'required|image',
            ],
            [],
            [
                'image' => __('family_member.fields.image.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('image');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members', $fileName, 'uploads');
            $inputs['image'] = "family_members/" . $fileName;
        }
        $request->user()->update([
            'image' => $inputs['image'],
        ]);
        return response()->json([
            'message' => __('family_member.messages.image_updated'),
            'data' => [
                'image' => $request->user()->image
            ],
        ]);
    }
    /**
     * Update Profile Cover
     *
     */
    public function updateProfileCover(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'cover_image' => 'required|image',
            ],
            [],
            [
                'cover_image' => __('family_member.fields.cover_image.label'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('cover_image');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/covers', $fileName, 'uploads');
            $inputs['cover_image'] = "family_members/covers/" . $fileName;
        }
        $request->user()->update([
            'cover_image' => $inputs['cover_image'],
        ]);
        return response()->json([
            'message' => __('family_member.messages.cover_image_updated'),
            'cover_image' => $request->user()->cover_image
        ]);
    }

    /**
     * Get Experiences
     *
     */
    public function getExperiences(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'nullable',
            ],
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $familyMemberId = $request->id;
        if ($familyMemberId) {
            // Load specific user with profile and experiences
            $user = FamilyMember::with(['profile.experiences'])->find($familyMemberId);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            // Load authenticated user with profile and experiences
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load(['profile.experiences']);
            }
        }

        // Get experiences through profile relationship or fallback to direct relationship
        $experiences = $user->profile ? $user->profile->experiences : $user->experiences;

        return response()->json(
            FamilyMemberExperienceResource::collection($experiences),
        );
    }

    /**
     * Create Experience
     *
     */
    public function createExperience(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'company_name' => 'required',
                'job_title' => 'required',
                'location' => 'required',
                'start_date' => 'required|date:Y-m-d',
                'end_date' => 'nullable|date:Y-m-d',
                'description' => 'required',
                'type' => 'required|in:job,training,volunteering',
            ],
            [],
            [
                'company_name' => __('Company name'),
                'job_title' => __('Job title'),
                'location' => __('Location'),
                'start_date' => __('Start date'),
                'end_date' => __('End date'),
                'description' => __('Description'),
                'type' => __('Type'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $user->load('profile');

        // Create experience through profile if available, otherwise through user
        if ($user->profile) {
            $user->profile->experiences()->create($inputs);
        } else {
            // Create profile first if it doesn't exist
            $profile = $user->profile()->create([]);
            $user->update(['profile_id' => $profile->id]);
            $profile->experiences()->create($inputs);
        }

        return response()->json([
            'message' => __('family_member.messages.experience_created'),
        ]);
    }

    /**
     * Update Experience
     *
     */
    public function updateExperience(Request $request, $id)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'company_name' => 'required',
                'job_title' => 'required',
                'location' => 'required',
                'start_date' => 'required|date:Y-m-d',
                'end_date' => 'nullable|date:Y-m-d',
                'description' => 'required',
                'type' => 'required|in:job,training,volunteering',
            ],
            [],
            [
                'company_name' => __('Company name'),
                'job_title' => __('Job title'),
                'location' => __('Location'),
                'start_date' => __('Start date'),
                'end_date' => __('End date'),
                'description' => __('Description'),
                'type' => __('Type'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->experiences()->where('id', $id)->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.experience_updated'),
            'data' => new FamilyMemberExperienceResource($request->user()->experiences()->where('id', $id)->first()),
        ]);
    }

    /**
     * Delete Experience
     */
    public function deleteExperience(Request $request, $id)
    {
        $request->user()->experiences()->where('id', $id)->delete();
        return response()->json([
            'message' => __('family_member.messages.experience_deleted'),
        ]);
    }

    public function getSkills(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'nullable',
            ],
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $familyMemberId = $request->id;
        if ($familyMemberId) {
            // Load specific user with profile and skills
            $user = FamilyMember::with(['profile.skills'])->find($familyMemberId);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            // Load authenticated user with profile and skills
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load(['profile.skills']);
            }
        }

        // Get skills through profile relationship or fallback to direct relationship
        $skills = $user->profile ? $user->profile->skills : $user->skills;

        return response()->json(
            SkillResource::collection($skills)
        );
    }

    public function createSkill(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required',
                'description' => 'required',
                'level' => 'required|numeric|min:0|max:100',
            ],
            [],
            [
                'name' => __('Name'),
                'description' => __('Description'),
                'level' => __('Level'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = $request->user();
        $user->load('profile');

        // Create skill through profile if available, otherwise through user
        if ($user->profile) {
            $user->profile->skills()->create($inputs);
        } else {
            // Create profile first if it doesn't exist
            $profile = $user->profile()->create([]);
            $user->update(['profile_id' => $profile->id]);
            $profile->skills()->create($inputs);
        }

        return response()->json([
            'message' => __('family_member.messages.skill_created'),
        ]);
    }

    public function updateSkill(Request $request, $id)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required',
                'description' => 'required',
                'level' => 'required|numeric|min:0|max:100',
            ],
            [],
            [
                'name' => __('Name'),
                'description' => __('Description'),
                'level' => __('Level'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $request->user()->skills()->where('id', $id)->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.skill_updated'),
        ]);
    }

    public function deleteSkill(Request $request, $id)
    {
        $request->user()->skills()->where('id', $id)->delete();
        return response()->json([
            'message' => __('family_member.messages.skill_deleted'),
        ]);
    }

    public function getAchievements(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'nullable',
            ],
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        $familyMemberId = $request->id;
        if ($familyMemberId) {
            // Load specific user with profile and achievements
            $user = FamilyMember::with(['profile.achievements'])->find($familyMemberId);
            if (!$user) {
                return response()->json(
                    [
                        'message' => __('family_member.messages.family_member_not_found')
                    ],
                    404
                );
            }
        } else {
            // Load authenticated user with profile and achievements
            $user = auth('sanctum')->user();
            if ($user) {
                $user->load(['profile.achievements']);
            }
        }

        // Get achievements through profile relationship or fallback to direct relationship
        $achievements = $user->profile ? $user->profile->achievements : $user->achievements;

        return response()->json(
            AchievementResource::collection($achievements)
        );
    }

    public function createAchievement(Request $request)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'title' => 'required',
                'description' => 'required',
                'file' => 'required|file',
                'date' => 'required|date',
                'type' => 'required|in:certificate,award,personal_achievement',
            ],
            [],
            [
                'title' => __('Title'),
                'description' => __('Description'),
                'file' => __('File'),
                'date' => __('Date'),
                'type' => __('Type'),
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }

        Log::info($inputs);

        $file = $request->file('file');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/achievements', $fileName, 'uploads');
            $inputs['file'] = "family_members/achievements/" . $fileName;
        }

        $user = $request->user();
        $user->load('profile');

        // Create achievement through profile if available, otherwise through user
        if ($user->profile) {
            $user->profile->achievements()->create($inputs);
        } else {
            // Create profile first if it doesn't exist
            $profile = $user->profile()->create([]);
            $user->update(['profile_id' => $profile->id]);
            $profile->achievements()->create($inputs);
        }

        return response()->json([
            'message' => __('family_member.messages.achievement_created'),
        ]);
    }

    public function updateAchievement(Request $request, $id)
    {
        $inputs = $request->all();
        $validator = Validator::make(
            $request->all(),
            [
                'title' => 'required',
                'description' => 'required',
                'file' => 'nullable|file',
                'date' => 'required|date',
                'type' => 'required|in:certificate,award,personal_achievement',
            ],
            [],
            [
                'title' => __('Title'),
                'description' => __('Description'),
                'file' => __('File'),
                'date' => __('Date'),
                'type' => __('Type'),
            ]
        );
        if ($validator->fails()) {
            return response()->json([
                'message' => __('common.messages.validation_error'),
                'errors' => $validator->errors(),
            ], 422);
        }
        $file = $request->file('file');
        if ($file) {
            // upload file to upload disk
            $fileName = date('Y-m-d-H-i-s') . '-' . rand(1000000, 9999999) . '.' . $file->getClientOriginalExtension();
            $file->storeAs('family_members/achievements', $fileName, 'uploads');
            $inputs['file'] = "family_members/achievements/" . $fileName;
        }
        $request->user()->achievements()->where('id', $id)->update($inputs);
        return response()->json([
            'message' => __('family_member.messages.achievement_updated'),
        ]);
    }

    public function deleteAchievement(Request $request, $id)
    {
        $request->user()->achievements()->where('id', $id)->delete();
        return response()->json([
            'message' => __('family_member.messages.achievement_deleted'),
        ]);
    }
}
