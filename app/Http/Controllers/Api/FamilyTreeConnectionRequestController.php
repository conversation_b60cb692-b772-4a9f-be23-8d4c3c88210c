<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Models\FamilyTreeConnectionRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

class FamilyTreeConnectionRequestController extends Controller
{
    /**
     * Create a new family tree connection request
     *
     * @authenticated
     *
     * @urlParam nodeId integer required The ID of the family tree node to connect to. Example: 1
     *
     * @bodyParam note string required A note explaining why the user wants to connect to this node. Example: "This is my grandfather's profile"
     *
     * @response scenario=success {
     *    "message": "تم إرسال طلب الربط بنجاح. سيتم مراجعته من قبل الإدارة.",
     *    "data": {
     *        "id": 1,
     *        "family_member_id": 123,
     *        "family_tree_node_id": 456,
     *        "status": "pending",
     *        "note": "This is my grandfather's profile",
     *        "requested_at": "2024-12-19T10:30:00Z"
     *    }
     * }
     *
     * @response 400 scenario="validation error" {
     *    "message": "خطأ في البيانات المدخلة.",
     *    "errors": {
     *        "note": ["الملاحظة مطلوبة."]
     *    }
     * }
     *
     * @response 409 scenario="already connected" {
     *    "message": "أنت مرتبط بالفعل بعقدة في الشجرة."
     * }
     *
     * @response 409 scenario="duplicate request" {
     *    "message": "لديك طلب ربط مُعلق بالفعل لهذه العقدة."
     * }
     *
     * @response 404 scenario="node not found" {
     *    "message": "عقدة الشجرة المطلوبة غير موجودة."
     * }
     *
     * @response 429 scenario="rate limit exceeded" {
     *    "message": "لقد تجاوزت الحد المسموح من الطلبات. يرجى المحاولة لاحقاً."
     * }
     *
     * @response 401 scenario=unauthenticated {
     *    "message": "Unauthenticated."
     * }
     *
     * @response 500 scenario="server error" {
     *    "message": "حدث خطأ في الخادم. يرجى المحاولة لاحقاً."
     * }
     */
    public function store(Request $request, int $nodeId): JsonResponse
    {
        try {
            // Rate limiting: Allow 3 requests per hour per user
            $key = 'connection-request:' . $request->user()->id;
            if (RateLimiter::tooManyAttempts($key, 3)) {
                return response()->json([
                    'message' => __('family_tree_connection_request.messages.rate_limit_exceeded'),
                ], 429);
            }

            // Validate input
            $validator = Validator::make($request->all(), [
                'note' => 'required|string|min:10|max:500',
            ], [
                'note.required' => __('family_tree_connection_request.validation.note.required'),
                'note.string' => __('family_tree_connection_request.validation.note.string'),
                'note.min' => __('family_tree_connection_request.validation.note.min', ['min' => 10]),
                'note.max' => __('family_tree_connection_request.validation.note.max', ['max' => 500]),
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => __('family_tree_connection_request.messages.validation_error'),
                    'errors' => $validator->errors(),
                ], 400);
            }

            // Check if the family tree node exists
            $familyTreeNode = FamilyTreeNode::find($nodeId);
            if (!$familyTreeNode) {
                return response()->json([
                    'message' => __('family_tree_connection_request.messages.node_not_found'),
                ], 404);
            }

            $familyMember = $request->user();

            // Check if user is already connected to any tree node
            if ($familyMember->family_tree_node_id) {
                return response()->json([
                    'message' => __('family_tree_connection_request.messages.already_connected'),
                ], 409);
            }

            // Check for existing pending request for this node
            $existingRequest = FamilyTreeConnectionRequest::where('family_member_id', $familyMember->id)
                ->where('family_tree_node_id', $nodeId)
                ->where('status', FamilyTreeConnectionRequest::STATUS_PENDING)
                ->first();

            if ($existingRequest) {
                return response()->json([
                    'message' => __('family_tree_connection_request.messages.duplicate_request'),
                ], 409);
            }

            // Create the connection request
            DB::beginTransaction();

            $connectionRequest = FamilyTreeConnectionRequest::create([
                'family_member_id' => $familyMember->id,
                'family_tree_node_id' => $nodeId,
                'status' => FamilyTreeConnectionRequest::STATUS_PENDING,
                'note' => $request->input('note'),
                'requested_at' => now(),
            ]);

            DB::commit();

            // Increment rate limiter
            RateLimiter::hit($key, 3600); // 1 hour

            // Log the request for admin notification
            Log::info('New family tree connection request', [
                'request_id' => $connectionRequest->id,
                'family_member_id' => $familyMember->id,
                'family_tree_node_id' => $nodeId,
                'member_name' => $familyMember->profile?->display_name ?? $familyMember->email,
                'node_name' => $familyTreeNode->name,
            ]);

            return response()->json([
                'message' => __('family_tree_connection_request.messages.request_created'),
                'data' => [
                    'id' => $connectionRequest->id,
                    'family_member_id' => $connectionRequest->family_member_id,
                    'family_tree_node_id' => $connectionRequest->family_tree_node_id,
                    'status' => $connectionRequest->status,
                    'note' => $connectionRequest->note,
                    'requested_at' => $connectionRequest->requested_at->toISOString(),
                ],
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Error creating family tree connection request', [
                'error' => $e->getMessage(),
                'family_member_id' => $request->user()->id ?? null,
                'family_tree_node_id' => $nodeId,
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => __('family_tree_connection_request.messages.server_error'),
            ], 500);
        }
    }
}
