<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AlbumJsonResource;
use App\Http\Resources\Api\Collections\AlbumCollection;
use App\Http\Resources\Api\FamilyTreeNodeJsonResource;
use App\Models\Album;
use App\Models\FamilyTreeNode;

class FamilyTreeNodeController extends Controller
{
    /**
     * Family Tree Nodes
     *
     * Get all Nodes
     *
     * @unauthenticated
     *
     * @response scenario=success
     *[
     *           {
     *              "id": 1,
     *              "title": "",
     *              "image": "",
     *              "media": [],
     *              "created_at": "",
     *            }
     *      ]
     *
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/
    public function index()
    {
        $inputs = request()->all();
        $data = FamilyTreeNode::with([
            'children.profile',
            'parent.profile',
            'family_member.profile',
            'profile'
        ])->orderBy('order', 'asc')->get();
        return response()->json(FamilyTreeNodeJsonResource::collection($data));
    }
    /**
     * albums
     *
     * Get Album by id
     *
     * @unauthenticated
     *
     *
     * @response scenario=success
     * {
     *
     *              "id": 1,
     *              "title": "",
     *              "image": "",
     *              "media": [],
     *              "created_at": ""
     *
     * }
     * @response 401 scenario=unauthenticated
     * {
     *    "message": "Unauthenticated."
     * }
     * @response 500 scenario="server error"
     **/

    public function show($id)
    {
        $album = Album::find($id);
        if (!$album) {
            return response()->json(['message' => __('common.messages.not_found')], 404);
        }
        return response()->json(new AlbumJsonResource($album));
    }
}
