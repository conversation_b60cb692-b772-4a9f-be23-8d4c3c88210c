<?php

namespace App\Console\Commands;

use App\Models\FamilyTreeNode;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PopulateFamilyTreeNodeNames extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'family-tree:populate-names
                            {--dry-run : Run without making changes to show what would be updated}
                            {--force : Force update even if name field is not empty}
                            {--csv-path= : Custom path to CSV file (default: resources/csv/family_tree_nodes.csv)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate empty name fields in FamilyTreeNode records using data from CSV file';

    /**
     * Statistics for the operation
     */
    private $stats = [
        'total_csv_records' => 0,
        'total_db_records' => 0,
        'matched_records' => 0,
        'updated_records' => 0,
        'skipped_records' => 0,
        'errors' => 0,
        'csv_parsing_errors' => 0,
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🌳 Starting Family Tree Node Name Population');
        $this->newLine();

        // Get CSV file path
        $csvPath = $this->option('csv-path') ?? resource_path('csv/family_tree_nodes.csv');

        if (!file_exists($csvPath)) {
            $this->error("❌ CSV file not found at: {$csvPath}");
            return Command::FAILURE;
        }

        $this->info("📁 Using CSV file: {$csvPath}");

        // Parse CSV and update records
        try {
            $csvData = $this->parseCsvFile($csvPath);
            $this->updateFamilyTreeNodes($csvData);
            $this->displayResults();

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("❌ Error: {$e->getMessage()}");
            Log::error('PopulateFamilyTreeNodeNames command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Parse the CSV file and return structured data
     */
    private function parseCsvFile(string $csvPath): array
    {
        $this->info('📊 Parsing CSV file...');

        $csvData = [];
        $handle = fopen($csvPath, 'r');

        if (!$handle) {
            throw new \Exception("Unable to open CSV file: {$csvPath}");
        }

        // Read header row
        $headers = fgetcsv($handle);
        if (!$headers) {
            throw new \Exception("Unable to read CSV headers");
        }

        // Find the ID and name column indices
        $idColumnIndex = null;
        $nameColumnIndex = null;

        foreach ($headers as $index => $header) {
            // Remove BOM, quotes, and trim whitespace
            $header = trim($header, "\"\xEF\xBB\xBF \t\n\r\0\x0B");
            if ($header === 'الرقم التعريفي') {
                $idColumnIndex = $index;
            } elseif ($header === 'الاسم') {
                $nameColumnIndex = $index;
            }
        }

        if ($idColumnIndex === null || $nameColumnIndex === null) {
            throw new \Exception("Required columns not found. Expected 'الرقم التعريفي' and 'الاسم'");
        }

        $this->info("✅ Found ID column at index {$idColumnIndex} and name column at index {$nameColumnIndex}");

        // Read data rows
        $lineNumber = 1; // Start from 1 since we already read headers
        while (($row = fgetcsv($handle)) !== false) {
            $lineNumber++;

            try {
                if (count($row) <= max($idColumnIndex, $nameColumnIndex)) {
                    $this->warn("⚠️  Line {$lineNumber}: Insufficient columns, skipping");
                    $this->stats['csv_parsing_errors']++;
                    continue;
                }

                $id = trim($row[$idColumnIndex]);
                $name = trim($row[$nameColumnIndex], '"');

                // Skip empty rows or rows without ID
                if (empty($id) || !is_numeric($id)) {
                    continue;
                }

                // Skip rows without name
                if (empty($name)) {
                    continue;
                }

                $csvData[(int)$id] = $name;
                $this->stats['total_csv_records']++;
            } catch (\Exception $e) {
                $this->warn("⚠️  Line {$lineNumber}: Error parsing - {$e->getMessage()}");
                $this->stats['csv_parsing_errors']++;
            }
        }

        fclose($handle);

        $this->info("✅ Parsed {$this->stats['total_csv_records']} valid records from CSV");
        if ($this->stats['csv_parsing_errors'] > 0) {
            $this->warn("⚠️  {$this->stats['csv_parsing_errors']} lines had parsing errors");
        }

        return $csvData;
    }

    /**
     * Update FamilyTreeNode records with names from CSV data
     */
    private function updateFamilyTreeNodes(array $csvData): void
    {
        $this->info('🔄 Processing FamilyTreeNode records...');

        // Get all FamilyTreeNode records
        $nodes = FamilyTreeNode::all();
        $this->stats['total_db_records'] = $nodes->count();

        $this->info("📊 Found {$this->stats['total_db_records']} records in database");

        $isDryRun = $this->option('dry-run');
        $forceUpdate = $this->option('force');

        if ($isDryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }

        $progressBar = $this->output->createProgressBar($nodes->count());
        $progressBar->start();

        foreach ($nodes as $node) {
            $progressBar->advance();

            try {
                // Check if we have CSV data for this node
                if (!isset($csvData[$node->id])) {
                    $this->stats['skipped_records']++;
                    continue;
                }

                $this->stats['matched_records']++;
                $csvName = $csvData[$node->id];

                // Check if we should update this record
                $shouldUpdate = false;
                $reason = '';

                if ($forceUpdate) {
                    $shouldUpdate = true;
                    $reason = 'force update';
                } elseif (empty($node->name)) {
                    $shouldUpdate = true;
                    $reason = 'empty name field';
                } else {
                    $reason = 'name field already populated';
                }

                if ($shouldUpdate) {
                    if (!$isDryRun) {
                        // Perform the actual update
                        DB::transaction(function () use ($node, $csvName) {
                            $node->update(['name' => $csvName]);
                        });
                    }

                    $this->stats['updated_records']++;

                    if ($this->output->isVerbose()) {
                        $this->line("✅ ID {$node->id}: '{$csvName}' ({$reason})");
                    }
                } else {
                    $this->stats['skipped_records']++;

                    if ($this->output->isVeryVerbose()) {
                        $this->line("⏭️  ID {$node->id}: Skipped ({$reason})");
                    }
                }
            } catch (\Exception $e) {
                $this->stats['errors']++;
                $this->error("❌ Error updating node ID {$node->id}: {$e->getMessage()}");

                Log::error('Error updating FamilyTreeNode', [
                    'node_id' => $node->id,
                    'error' => $e->getMessage(),
                    'csv_name' => $csvData[$node->id] ?? null
                ]);
            }
        }

        $progressBar->finish();
        $this->newLine(2);
    }

    /**
     * Display the results of the operation
     */
    private function displayResults(): void
    {
        $this->info('📈 Operation Summary:');
        $this->newLine();

        // Create a table for the results
        $this->table(
            ['Metric', 'Count'],
            [
                ['CSV Records Parsed', $this->stats['total_csv_records']],
                ['Database Records Found', $this->stats['total_db_records']],
                ['Records Matched (ID found in CSV)', $this->stats['matched_records']],
                ['Records Updated', $this->stats['updated_records']],
                ['Records Skipped', $this->stats['skipped_records']],
                ['CSV Parsing Errors', $this->stats['csv_parsing_errors']],
                ['Update Errors', $this->stats['errors']],
            ]
        );

        // Success/failure summary
        if ($this->stats['errors'] === 0) {
            $this->info('✅ Operation completed successfully!');
        } else {
            $this->warn("⚠️  Operation completed with {$this->stats['errors']} errors. Check logs for details.");
        }

        // Recommendations
        if ($this->stats['matched_records'] < $this->stats['total_db_records']) {
            $unmatchedCount = $this->stats['total_db_records'] - $this->stats['matched_records'];
            $this->warn("ℹ️  {$unmatchedCount} database records had no matching CSV data");
        }

        if ($this->option('dry-run') && $this->stats['updated_records'] > 0) {
            $this->info("💡 Run without --dry-run to apply {$this->stats['updated_records']} updates");
        }

        // Log summary
        Log::info('PopulateFamilyTreeNodeNames completed', [
            'stats' => $this->stats,
            'dry_run' => $this->option('dry-run'),
            'force' => $this->option('force'),
        ]);
    }
}
