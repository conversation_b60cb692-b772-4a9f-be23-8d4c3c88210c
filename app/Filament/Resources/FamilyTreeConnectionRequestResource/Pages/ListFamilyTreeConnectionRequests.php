<?php

namespace App\Filament\Resources\FamilyTreeConnectionRequestResource\Pages;

use App\Filament\Resources\FamilyTreeConnectionRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFamilyTreeConnectionRequests extends ListRecords
{
    protected static string $resource = FamilyTreeConnectionRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
