<?php

namespace App\Filament\Resources\FamilyTreeConnectionRequestResource\Pages;

use App\Filament\Resources\FamilyTreeConnectionRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFamilyTreeConnectionRequest extends EditRecord
{
    protected static string $resource = FamilyTreeConnectionRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
