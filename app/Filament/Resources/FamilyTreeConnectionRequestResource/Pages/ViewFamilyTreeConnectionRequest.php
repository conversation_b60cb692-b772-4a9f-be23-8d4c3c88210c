<?php

namespace App\Filament\Resources\FamilyTreeConnectionRequestResource\Pages;

use App\Filament\Resources\FamilyTreeConnectionRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewFamilyTreeConnectionRequest extends ViewRecord
{
    protected static string $resource = FamilyTreeConnectionRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
