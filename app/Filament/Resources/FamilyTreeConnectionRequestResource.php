<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FamilyTreeConnectionRequestResource\Pages;
use App\Models\FamilyTreeConnectionRequest;
use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Traits\HasLabelTranslation;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;

class FamilyTreeConnectionRequestResource extends Resource
{
    use HasLabelTranslation;

    protected static ?string $model = FamilyTreeConnectionRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-link';

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.family_tree");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\Select::make('family_member_id')
                        ->label(__('family_tree_connection_request.fields.family_member_id.label'))
                        ->options(function () {
                            return FamilyMember::with('profile')
                                ->get()
                                ->mapWithKeys(function ($member) {
                                    $displayName = $member->profile?->display_name;
                                    $label = (!empty(trim($displayName))) ? $displayName : ($member->email ?? 'Unknown Member');
                                    return [$member->id => $label];
                                })
                                ->toArray();
                        })
                        ->searchable()
                        ->required()
                        ->disabledOn('edit'),

                    Forms\Components\Select::make('family_tree_node_id')
                        ->label(__('family_tree_connection_request.fields.family_tree_node_id.label'))
                        ->options(function () {
                            return FamilyTreeNode::with('profile')
                                ->get()
                                ->mapWithKeys(function ($node) {
                                    $displayName = $node->profile?->display_name ?? $node->name;
                                    $label = (!empty(trim($displayName))) ? $displayName : 'Unknown Node #' . $node->id;
                                    return [$node->id => $label];
                                })
                                ->toArray();
                        })
                        ->searchable()
                        ->required()
                        ->disabledOn('edit'),

                    Forms\Components\Select::make('status')
                        ->label(__('family_tree_connection_request.fields.status.label'))
                        ->options(__('family_tree_connection_request.status_options'))
                        ->required()
                        ->default(FamilyTreeConnectionRequest::STATUS_PENDING),

                    Forms\Components\Textarea::make('note')
                        ->label(__('family_tree_connection_request.fields.note.label'))
                        ->rows(3)
                        ->columnSpanFull(),

                    Forms\Components\DateTimePicker::make('requested_at')
                        ->label(__('family_tree_connection_request.fields.requested_at.label'))
                        ->default(now())
                        ->disabledOn('edit'),

                    Forms\Components\DateTimePicker::make('reviewed_at')
                        ->label(__('family_tree_connection_request.fields.reviewed_at.label'))
                        ->visible(fn($context) => $context === 'edit'),

                    Forms\Components\Select::make('reviewed_by')
                        ->label(__('family_tree_connection_request.fields.reviewed_by.label'))
                        ->relationship('reviewedBy', 'name')
                        ->visibleOn('edit')
                        ->disabled(),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('family_tree_connection_request.columns.id'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('familyMember.profile.display_name')
                    ->label(__('family_tree_connection_request.columns.family_member_name'))
                    ->getStateUsing(function ($record) {
                        $displayName = $record->familyMember?->profile?->display_name;
                        return (!empty(trim($displayName))) ? $displayName : ($record->familyMember?->email ?? 'Unknown Member');
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('familyTreeNode.name')
                    ->label(__('family_tree_connection_request.columns.family_tree_node_name'))
                    ->getStateUsing(function ($record) {
                        $displayName = $record->familyTreeNode?->profile?->display_name ?? $record->familyTreeNode?->name;
                        return (!empty(trim($displayName))) ? $displayName : 'Unknown Node #' . $record->family_tree_node_id;
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label(__('family_tree_connection_request.columns.status'))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        FamilyTreeConnectionRequest::STATUS_PENDING => 'warning',
                        FamilyTreeConnectionRequest::STATUS_APPROVED => 'success',
                        FamilyTreeConnectionRequest::STATUS_REJECTED => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn($state) => __('family_tree_connection_request.status_options.' . $state)),

                Tables\Columns\TextColumn::make('requested_at')
                    ->label(__('family_tree_connection_request.columns.requested_at'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('reviewed_at')
                    ->label(__('family_tree_connection_request.columns.reviewed_at'))
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('note')
                    ->label(__('family_tree_connection_request.columns.note'))
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label(__('family_tree_connection_request.fields.status.label'))
                    ->options(__('family_tree_connection_request.status_options')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\Action::make('approve')
                    ->label(__('family_tree_connection_request.actions.approve'))
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn(FamilyTreeConnectionRequest $record) => $record->isPending())
                    ->requiresConfirmation()
                    ->action(function (FamilyTreeConnectionRequest $record) {
                        $record->approve(auth()->id());
                    })
                    ->successNotificationTitle(__('family_tree_connection_request.messages.request_approved')),

                Tables\Actions\Action::make('reject')
                    ->label(__('family_tree_connection_request.actions.reject'))
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->visible(fn(FamilyTreeConnectionRequest $record) => $record->isPending())
                    ->requiresConfirmation()
                    ->action(function (FamilyTreeConnectionRequest $record) {
                        $record->reject(auth()->id());
                    })
                    ->successNotificationTitle(__('family_tree_connection_request.messages.request_rejected')),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('requested_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFamilyTreeConnectionRequests::route('/'),
            'create' => Pages\CreateFamilyTreeConnectionRequest::route('/create'),
            'view' => Pages\ViewFamilyTreeConnectionRequest::route('/{record}'),
            'edit' => Pages\EditFamilyTreeConnectionRequest::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['familyMember.profile', 'familyTreeNode.profile', 'reviewedBy']);
    }
}
