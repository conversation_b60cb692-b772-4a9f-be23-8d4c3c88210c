<?php

namespace App\Filament\Resources;

use App\Filament\Actions\Table\ActivateAction;
use App\Filament\Actions\Table\ActivateBulkAction;
use App\Filament\Actions\Table\DeactivateAction;
use App\Filament\Resources\FamilyMemberResource\Pages;
use App\Filament\Resources\FamilyMemberResource\RelationManagers;
use App\Models\FamilyMember;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasLabelTranslation;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Toggle;
use Illuminate\Contracts\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;

class FamilyMemberResource extends Resource
{

    use HasLabelTranslation;

    protected static ?string $model = FamilyMember::class;

    protected static ?string $navigationIcon = 'heroicon-o-user';

    public static function getNavigationGroup(): string
    {
        return __("common.navigation_groups.family");
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\FileUpload::make('image')
                        ->previewable()
                        ->downloadable()
                        ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg'])
                        // ->disabledOn('edit')
                        ->directory('family_member/images'),
                    Forms\Components\TextInput::make('first_name')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('middle_name')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('last_name')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('grandfather_name')
                        ->maxLength(255),
                    Forms\Components\Select::make('branch_id')
                        ->options(fn() => \App\Models\FamilyBranch::all()->pluck('name', 'id')),
                    Forms\Components\Select::make('gender')
                        ->options(__('common.options.gender'))
                        ->required(),
                    Forms\Components\TextInput::make('country_code')
                        ->required(),
                    Forms\Components\TextInput::make('mobile')
                        ->required()
                        // ->disabledOn('edit')
                        ->maxLength(255),
                    Forms\Components\TextInput::make('email')
                        ->email()
                        ->disabledOn('edit')
                        ->maxLength(255),
                    Forms\Components\Select::make('country_id')
                        ->options(fn() => \App\Models\Country::all()->pluck('name', 'id'))
                        ->live()
                        ->required(),
                    Forms\Components\Select::make('city_id')
                        ->options(fn($get) => \App\Models\City::where('country_id', $get('country_id'))->pluck('name', 'id')),
                    Forms\Components\Textarea::make('address'),
                    Forms\Components\TextInput::make('password')
                        ->password()
                        ->required()
                        ->disabledOn('edit')
                        ->visibleOn('create')
                        ->dehydrateStateUsing(fn($state) => Hash::make($state))
                        ->disableAutoComplete()
                        ->maxLength(255),
                    Forms\Components\FileUpload::make('cv_file')
                        ->previewable()
                        ->downloadable()
                        ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg', 'application/pdf'])
                        // ->disabledOn('edit')
                        ->directory('family_member/cv'),
                    Toggle::make('status'),
                ])->columnSpan(
                    [
                        "sm" => 2
                    ]
                ),
                Forms\Components\Section::make([
                    Forms\Components\Placeholder::make('created_at')
                        ->content(fn($record): string => $record ? $record->created_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('updated_at')
                        ->content(fn($record): string => $record ? $record->updated_at->diffForHumans() : '-'),
                    Forms\Components\Placeholder::make('deleted_at')
                        ->visible(fn($record): bool => $record?->deleted_at ? true : false)
                        ->content(fn($record): string => $record ? $record->deleted_at->diffForHumans() : '-'),
                ])->columnSpan(1),
            ])->columns(
                [
                    "sm" => 3,
                    "lg" => null,
                ]
            );
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                // Tables\Columns\ImageColumn::make('image'),
                Tables\Columns\TextColumn::make('profile.first_name')
                    ->searchable()
                    ->label(__('family_member.fields.first_name.label')),
                Tables\Columns\TextColumn::make('profile.middle_name')
                    ->searchable()
                    ->label(__('family_member.fields.middle_name.label')),
                Tables\Columns\TextColumn::make('profile.last_name')
                    ->searchable()
                    ->label(__('family_member.fields.last_name.label')),
                // Tables\Columns\TextColumn::make('profile.grandfather_name')
                //     ->searchable(),
                Tables\Columns\TextColumn::make('branch.name'),
                Tables\Columns\TextColumn::make('profile.gender')
                    ->formatStateUsing(fn($state) => __('common.options.gender')[$state]),
                Tables\Columns\IconColumn::make('status')
                    ->boolean(),
                Tables\Columns\TextColumn::make('mobile')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('Y-m-d h:i A'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('Y-m-d h:i A'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\SelectFilter::make('status')
                    ->label('حالة الحساب')
                    ->options([
                        0 => 'معطل',
                        1 => 'مفعل',
                    ])
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                ActivateAction::make(),
                DeactivateAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    ActivateBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFamilyMembers::route('/'),
            'create' => Pages\CreateFamilyMember::route('/create'),
            'view' => Pages\ViewFamilyMember::route('/{record}'),
            'edit' => Pages\EditFamilyMember::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    protected function shouldPersistTableFiltersInSession(): bool
    {
        return false;
    }

    public static $permissions = [
        'view',
        'view_any',
        'create',
        'update',
        'restore',
        'restore_any',
        // 'replicate',
        // 'reorder',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
    ];
}
