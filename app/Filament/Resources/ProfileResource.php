<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProfileResource\Pages;
use App\Models\Profile;
use App\Traits\HasLabelTranslation;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ProfileResource extends Resource
{
    use HasLabelTranslation;

    protected static ?string $model = Profile::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    public static function getModelLabel(): string
    {
        return __('profile.singular_label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('profile.plural_label');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('common.navigation_groups.family');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('profile.sections.basic_information'))
                    ->schema([
                        TextInput::make('first_name')
                            ->label(__('profile.fields.first_name.label'))
                            ->placeholder(__('profile.fields.first_name.placeholder'))
                            ->required()
                            ->maxLength(255),
                        TextInput::make('middle_name')
                            ->label(__('profile.fields.middle_name.label'))
                            ->placeholder(__('profile.fields.middle_name.placeholder'))
                            ->maxLength(255),
                        TextInput::make('last_name')
                            ->label(__('profile.fields.last_name.label'))
                            ->placeholder(__('profile.fields.last_name.placeholder'))
                            ->required()
                            ->maxLength(255),
                        TextInput::make('grandfather_name')
                            ->label(__('profile.fields.grandfather_name.label'))
                            ->placeholder(__('profile.fields.grandfather_name.placeholder'))
                            ->maxLength(255),
                        TextInput::make('nick_name')
                            ->label(__('profile.fields.nick_name.label'))
                            ->placeholder(__('profile.fields.nick_name.placeholder'))
                            ->maxLength(255),
                        Select::make('gender')
                            ->label(__('profile.fields.gender.label'))
                            ->placeholder(__('profile.fields.gender.placeholder'))
                            ->options(__('profile.gender_options')),
                    ])->columns(2),

                Section::make(__('profile.sections.dates_and_places'))
                    ->schema([
                        DatePicker::make('birth_date')
                            ->label(__('profile.fields.birth_date.label'))
                            ->placeholder(__('profile.fields.birth_date.placeholder')),
                        DatePicker::make('death_date')
                            ->label(__('profile.fields.death_date.label'))
                            ->placeholder(__('profile.fields.death_date.placeholder')),
                        Textarea::make('birth_place')
                            ->label(__('profile.fields.birth_place.label'))
                            ->placeholder(__('profile.fields.birth_place.placeholder'))
                            ->rows(2),
                        Textarea::make('death_place')
                            ->label(__('profile.fields.death_place.label'))
                            ->placeholder(__('profile.fields.death_place.placeholder'))
                            ->rows(2),
                    ])->columns(2),

                Section::make(__('profile.sections.description_and_work'))
                    ->schema([
                        Textarea::make('overview')
                            ->label(__('profile.fields.overview.label'))
                            ->placeholder(__('profile.fields.overview.placeholder'))
                            ->rows(4)
                            ->columnSpanFull(),
                        Textarea::make('job')
                            ->label(__('profile.fields.job.label'))
                            ->placeholder(__('profile.fields.job.placeholder'))
                            ->rows(2),
                        Textarea::make('address')
                            ->label(__('profile.fields.address.label'))
                            ->placeholder(__('profile.fields.address.placeholder'))
                            ->rows(2),
                    ])->columns(2),

                Section::make(__('profile.sections.media'))
                    ->schema([
                        FileUpload::make('image')
                            ->label(__('profile.fields.image.label'))
                            ->image()
                            ->directory('profiles'),
                        FileUpload::make('cover_image')
                            ->label(__('profile.fields.cover_image.label'))
                            ->image()
                            ->directory('profiles/covers'),
                    ])->columns(2),

                Section::make(__('profile.sections.social_media_links'))
                    ->schema([
                        KeyValue::make('social_links')
                            ->label(__('profile.fields.social_links.label'))
                            ->keyLabel(__('profile.fields.social_links.key_label'))
                            ->valueLabel(__('profile.fields.social_links.value_label'))
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label(__('profile.columns.id'))
                    ->sortable(),
                TextColumn::make('display_name')
                    ->label(__('profile.columns.name'))
                    ->searchable(['first_name', 'middle_name', 'last_name'])
                    ->sortable(),
                TextColumn::make('nick_name')
                    ->label(__('profile.columns.nick_name'))
                    ->searchable(),
                TextColumn::make('gender')
                    ->label(__('profile.columns.gender'))
                    ->formatStateUsing(fn(?string $state): string => match ($state) {
                        '1' => __('profile.gender_options.1'),
                        '2' => __('profile.gender_options.2'),
                        default => '-',
                    }),
                TextColumn::make('birth_date')
                    ->label(__('profile.columns.birth_date'))
                    ->date()
                    ->sortable(),
                TextColumn::make('death_date')
                    ->label(__('profile.columns.death_date'))
                    ->date()
                    ->sortable(),
                TextColumn::make('job')
                    ->label(__('profile.columns.job'))
                    ->searchable(),
                TextColumn::make('familyMembers.email')
                    ->label(__('profile.columns.email'))
                    ->badge()
                    ->separator(','),
                TextColumn::make('familyTreeNodes.name')
                    ->label(__('profile.columns.tree_nodes'))
                    ->badge()
                    ->separator(','),
                TextColumn::make('created_at')
                    ->label(__('profile.columns.created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('gender')
                    ->label(__('profile.filters.gender'))
                    ->options(__('profile.gender_options')),
                Tables\Filters\Filter::make('has_living_members')
                    ->query(fn(Builder $query): Builder => $query->whereHas('familyMembers'))
                    ->label(__('profile.filters.has_living_members')),
                Tables\Filters\Filter::make('deceased')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('death_date'))
                    ->label(__('profile.filters.deceased')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProfiles::route('/'),
            'create' => Pages\CreateProfile::route('/create'),
            'edit' => Pages\EditProfile::route('/{record}/edit'),
        ];
    }
}
