<?php

namespace App\Filament\Resources\GreetingCardResource\Pages;

use App\Filament\Resources\GreetingCardResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateGreetingCard extends CreateRecord
{
    protected static string $resource = GreetingCardResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // $data['text_coordinate']["x"] = 50;
        // $data['name_coordinate']["x"] = 50;
        // $data['text_x'] = $coordinate["x"];
        // $data['text_y'] = $coordinate["y"];
        // unset($data['text_coordinate']);
        // dd($data);
        return $data;
    }
}
