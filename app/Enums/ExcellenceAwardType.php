<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum ExcellenceAwardType: string implements Has<PERSON>abel, HasColor, HasIcon, HasDescription
{
    case AcademicExcellence = 'academic_excellence';
    case QuranMemorization = 'quran_memorization';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::AcademicExcellence => __('excellence_award.types.academic_excellence'),
            self::QuranMemorization => __('excellence_award.types.quran_memorization'),
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::AcademicExcellence => 'success',
            self::QuranMemorization => 'info',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::AcademicExcellence => 'heroicon-m-academic-cap',
            self::QuranMemorization => 'heroicon-m-book-open',
        };
    }

    public function getDescription(): ?string
    {
        return match ($this) {
            self::AcademicExcellence => __('excellence_award.types.academic_excellence_description'),
            self::QuranMemorization => __('excellence_award.types.quran_memorization_description'),
        };
    }

    /**
     * @deprecated Use the enum directly with Filament components instead
     */
    public static function getOptions(): array
    {
        return [
            self::AcademicExcellence->value => self::AcademicExcellence->getLabel(),
            self::QuranMemorization->value => self::QuranMemorization->getLabel(),
        ];
    }
}
