<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FamilyTreeConnectionRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'family_member_id',
        'family_tree_node_id',
        'status',
        'note',
        'requested_at',
        'reviewed_at',
        'reviewed_by',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'reviewed_at' => 'datetime',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';

    /**
     * Get the family member who made the request
     */
    public function familyMember(): BelongsTo
    {
        return $this->belongsTo(FamilyMember::class);
    }

    /**
     * Get the family tree node being requested for connection
     */
    public function familyTreeNode(): BelongsTo
    {
        return $this->belongsTo(FamilyTreeNode::class);
    }

    /**
     * Get the admin user who reviewed the request
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope for rejected requests
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * Check if the request is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the request is approved
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if the request is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Approve the connection request
     */
    public function approve(int $reviewedBy = null): bool
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy,
        ]);

        // Link the family member to the tree node
        $this->familyMember->update([
            'family_tree_node_id' => $this->family_tree_node_id
        ]);

        // Also link the tree node to the family member if not already linked
        if (!$this->familyTreeNode->family_member_id) {
            $this->familyTreeNode->update([
                'family_member_id' => $this->family_member_id
            ]);
        }

        return true;
    }

    /**
     * Reject the connection request
     */
    public function reject(int $reviewedBy = null): bool
    {
        return $this->update([
            'status' => self::STATUS_REJECTED,
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy,
        ]);
    }
}
