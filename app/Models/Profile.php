<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Profile extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'grandfather_name',
        'nick_name',
        'gender',
        'birth_date',
        'death_date',
        'birth_place',
        'death_place',
        'overview',
        'image',
        'cover_image',
        'address',
        'job',
        'social_links',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'death_date' => 'date',
        'social_links' => 'array',
    ];

    // Relationships to core models
    public function familyMembers(): HasMany
    {
        return $this->hasMany(FamilyMember::class);
    }

    public function familyTreeNodes(): HasMany
    {
        return $this->hasMany(FamilyTreeNode::class);
    }

    // Profile-related data relationships
    public function skills(): HasMany
    {
        return $this->hasMany(Skill::class);
    }

    public function experiences(): HasMany
    {
        return $this->hasMany(Experience::class);
    }

    public function achievements(): HasMany
    {
        return $this->hasMany(Achievement::class);
    }

    public function reviewsGiven(): HasMany
    {
        return $this->hasMany(Review::class, 'reviewer_profile_id');
    }

    public function reviewsReceived(): HasMany
    {
        return $this->hasMany(Review::class, 'reviewed_profile_id');
    }

    public function activeReviewsReceived(): HasMany
    {
        return $this->hasMany(Review::class, 'reviewed_profile_id')->active();
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    // Computed attributes
    public function displayName(): Attribute
    {
        return Attribute::make(
            get: fn($value) => trim($this->first_name . ' ' . $this->middle_name . ' ' . $this->last_name)
        );
    }

    public function fullName(): Attribute
    {
        return Attribute::make(
            get: fn($value) => trim($this->first_name . ' ' . $this->middle_name . ' ' . $this->last_name . ' ' . $this->grandfather_name)
        );
    }

    public function thumbImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->image ? url('/images/thumb/' . $this->image) : url('/assets/images/male-avatar.png')
        );
    }

    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->image ? url('uploads/' . $this->image) : url('/assets/images/male-avatar.png')
        );
    }

    public function coverImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->cover_image ? url('uploads/' . $this->cover_image) : null
        );
    }

    // Social media link accessors
    public function facebookLink(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->social_links['facebook'] ?? null
        );
    }

    public function xLink(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->social_links['x'] ?? null
        );
    }

    public function snapshotLink(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->social_links['snapshot'] ?? null
        );
    }

    public function youtubeLink(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->social_links['youtube'] ?? null
        );
    }

    public function linkedinLink(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->social_links['linkedin'] ?? null
        );
    }

    public function instagramLink(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->social_links['instagram'] ?? null
        );
    }

    // Review statistics (moved from FamilyMember)
    public function getAverageRatingAttribute()
    {
        return $this->activeReviewsReceived()->avg('rating') ?: 0;
    }

    public function getReviewsCountAttribute()
    {
        return $this->activeReviewsReceived()->count();
    }

    public function getReviewStatsAttribute()
    {
        $reviews = $this->activeReviewsReceived()->get();
        $count = $reviews->count();
        $average = $count > 0 ? round($reviews->avg('rating'), 1) : 0;

        return [
            'count' => $count,
            'average' => $average,
            'ratings_breakdown' => (object) [
                '5' => $reviews->where('rating', 5)->count(),
                '4' => $reviews->where('rating', 4)->count(),
                '3' => $reviews->where('rating', 3)->count(),
                '2' => $reviews->where('rating', 2)->count(),
                '1' => $reviews->where('rating', 1)->count(),
            ]
        ];
    }

    // Helper methods
    public function isAlive(): bool
    {
        return is_null($this->death_date);
    }

    public function hasLivingMember(): bool
    {
        return $this->familyMembers()->exists();
    }

    public function getPrimaryFamilyMember()
    {
        return $this->familyMembers()->first();
    }

    public function getPrimaryTreeNode()
    {
        return $this->familyTreeNodes()->first();
    }
}
