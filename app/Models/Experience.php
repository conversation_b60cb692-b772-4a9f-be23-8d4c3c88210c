<?php

namespace App\Models;

use App\Enums\ExperienceType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Experience extends Model
{
    use HasFactory;
    // fillable
    protected $fillable = [
        'job_title',
        'description',
        'company_name',
        'location',
        'start_date',
        'end_date',
        'type',
        'profile_id',
        'family_member_id', // Legacy field - will be removed after migration
    ];

    // cast
    protected $casts = [
        'type' => ExperienceType::class,
    ];

    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    // Legacy relationship - will be removed after migration
    public function family_member()
    {
        return $this->belongsTo(FamilyMember::class);
    }
}
