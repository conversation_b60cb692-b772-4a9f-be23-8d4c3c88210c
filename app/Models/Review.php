<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Review extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reviewer_profile_id',
        'reviewed_profile_id',
        'rating',
        'comment',
        'status',
        // Legacy fields - will be removed after migration
        'reviewer_id',
        'reviewed_user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating' => 'integer',
        'status' => 'boolean',
    ];

    /**
     * Validation rules for the model.
     *
     * @return array<string, string>
     */
    public static function validationRules(): array
    {
        return [
            'reviewer_profile_id' => 'required|exists:profiles,id',
            'reviewed_profile_id' => 'required|exists:profiles,id|different:reviewer_profile_id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
            'status' => 'boolean',
        ];
    }

    /**
     * Get the reviewer profile.
     */
    public function reviewerProfile(): BelongsTo
    {
        return $this->belongsTo(Profile::class, 'reviewer_profile_id');
    }

    /**
     * Get the reviewed profile.
     */
    public function reviewedProfile(): BelongsTo
    {
        return $this->belongsTo(Profile::class, 'reviewed_profile_id');
    }

    /**
     * Legacy: Get the reviewer (family member who wrote the review).
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(FamilyMember::class, 'reviewer_id');
    }

    /**
     * Legacy: Get the reviewed user (family member being reviewed).
     */
    public function reviewedUser(): BelongsTo
    {
        return $this->belongsTo(FamilyMember::class, 'reviewed_user_id');
    }

    /**
     * Scope a query to only include active reviews.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include reviews for a specific profile.
     */
    public function scopeForProfile($query, $profileId)
    {
        return $query->where('reviewed_profile_id', $profileId);
    }

    /**
     * Scope a query to only include reviews by a specific profile.
     */
    public function scopeByProfile($query, $profileId)
    {
        return $query->where('reviewer_profile_id', $profileId);
    }

    /**
     * Legacy: Scope a query to only include reviews for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('reviewed_user_id', $userId);
    }

    /**
     * Legacy: Scope a query to only include reviews by a specific reviewer.
     */
    public function scopeByReviewer($query, $reviewerId)
    {
        return $query->where('reviewer_id', $reviewerId);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure a profile cannot review itself
        static::creating(function ($review) {
            // Check new profile-based fields
            if (
                $review->reviewer_profile_id && $review->reviewed_profile_id &&
                $review->reviewer_profile_id === $review->reviewed_profile_id
            ) {
                throw new \InvalidArgumentException('A profile cannot review itself.');
            }
            // Legacy check for old fields
            if (
                $review->reviewer_id && $review->reviewed_user_id &&
                $review->reviewer_id === $review->reviewed_user_id
            ) {
                throw new \InvalidArgumentException('A user cannot review themselves.');
            }
        });

        static::updating(function ($review) {
            // Check new profile-based fields
            if (
                $review->reviewer_profile_id && $review->reviewed_profile_id &&
                $review->reviewer_profile_id === $review->reviewed_profile_id
            ) {
                throw new \InvalidArgumentException('A profile cannot review itself.');
            }
            // Legacy check for old fields
            if (
                $review->reviewer_id && $review->reviewed_user_id &&
                $review->reviewer_id === $review->reviewed_user_id
            ) {
                throw new \InvalidArgumentException('A user cannot review themselves.');
            }
        });
    }
}
