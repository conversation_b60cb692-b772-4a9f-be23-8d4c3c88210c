<?php

namespace App\Models;

use App\Enums\AchievementType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Achievement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'file',
        'type',
        'date',
        'profile_id',
        'family_member_id', // Legacy field - will be removed after migration
    ];

    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    // Legacy relationship - will be removed after migration
    public function familyMember()
    {
        return $this->belongsTo(FamilyMember::class);
    }

    // casting
    protected $casts = [
        'type' => AchievementType::class,
    ];
}
