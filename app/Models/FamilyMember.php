<?php

namespace App\Models;

use App\Facades\Whatsapp;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;

class FamilyMember extends Authenticatable
{
    use HasFactory;
    use SoftDeletes;
    use HasApiTokens;

    protected $fillable = [
        'profile_id',
        'email',
        'mobile',
        'branch_id',
        'password',
        'status',
        'reset_password_code',
        'country_id',
        'city_id',
        'cv_file',
        'cv_type',
        'cv_text',
        'country_code',
        'family_tree_node_id',
    ];
    protected $casts = [
        // Legacy casts removed - now handled by Profile model
    ];

    // Profile relationship (central data)
    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    // branch relationship
    public function branch()
    {
        return $this->belongsTo(FamilyBranch::class, 'branch_id');
    }
    // country relationship
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }
    // city relationship
    public function city()
    {
        return $this->belongsTo(City::class, 'city_id');
    }


    // User-specific relationships (keep with FamilyMember)
    public function devices()
    {
        return $this->hasMany(FamilyMemberDevice::class, 'family_member_id');
    }

    public function treeRequests()
    {
        return $this->hasMany(FamilyTreeRequest::class, 'family_member_id');
    }

    public function connectionRequests()
    {
        return $this->hasMany(FamilyTreeConnectionRequest::class, 'family_member_id');
    }

    public function visits()
    {
        return $this->hasMany(Visit::class, 'user_id');
    }

    public function commentLikes()
    {
        return $this->hasMany(CommentLike::class, 'family_member_id');
    }

    // Profile-related relationships (delegate to profile)
    public function experiences()
    {
        return $this->profile ? $this->profile->experiences() : $this->hasMany(Experience::class, 'family_member_id');
    }

    public function skills()
    {
        return $this->profile ? $this->profile->skills() : collect();
    }

    public function achievements()
    {
        return $this->profile ? $this->profile->achievements() : $this->hasMany(Achievement::class, 'family_member_id');
    }

    public function reviewsGiven()
    {
        return $this->profile ? $this->profile->reviewsGiven() : $this->hasMany(Review::class, 'reviewer_id');
    }

    public function reviewsReceived()
    {
        return $this->profile ? $this->profile->reviewsReceived() : $this->hasMany(Review::class, 'reviewed_user_id');
    }

    public function activeReviewsReceived()
    {
        return $this->profile ? $this->profile->activeReviewsReceived() : $this->hasMany(Review::class, 'reviewed_user_id')->active();
    }

    public function comments()
    {
        return $this->profile ? $this->profile->comments() : $this->hasMany(Comment::class, 'family_member_id');
    }

    // Delegate to profile (legacy fields removed)
    public function displayName(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->profile
                ? $this->profile->display_name
                : 'Unknown'
        );
    }

    public function thumbImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->profile
                ? $this->profile->thumb_image_url
                : url('/assets/images/male-avatar.png')
        );
    }

    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->profile
                ? $this->profile->image_url
                : url('/assets/images/male-avatar.png')
        );
    }


    public function getFcmTokens()
    {
        return $this->devices()->pluck('fcm_token')->toArray();
    }

    public function whatsappNumber(): Attribute
    {
        return Attribute::make(
            get: fn($value) => str($this->country_code)->replace('+', '')->toString() . $this->mobile
        );
    }

    /**
     * Get the average rating for this user (delegate to profile).
     */
    public function getAverageRatingAttribute()
    {
        return $this->profile ? $this->profile->average_rating : ($this->activeReviewsReceived()->avg('rating') ?: 0);
    }

    /**
     * Get the total number of reviews for this user (delegate to profile).
     */
    public function getReviewsCountAttribute()
    {
        return $this->profile ? $this->profile->reviews_count : $this->activeReviewsReceived()->count();
    }

    /**
     * Get review statistics for this user (delegate to profile).
     */
    public function getReviewStatsAttribute()
    {
        if ($this->profile) {
            return $this->profile->review_stats;
        }

        // Fallback to legacy calculation
        $reviews = $this->activeReviewsReceived()->get();
        $count = $reviews->count();
        $average = $count > 0 ? round($reviews->avg('rating'), 1) : 0;

        return [
            'count' => $count,
            'average' => $average,
            'ratings_breakdown' => (object) [
                '5' => $reviews->where('rating', 5)->count(),
                '4' => $reviews->where('rating', 4)->count(),
                '3' => $reviews->where('rating', 3)->count(),
                '2' => $reviews->where('rating', 2)->count(),
                '1' => $reviews->where('rating', 1)->count(),
            ]
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::updated(function ($familyMember) {
            if ($familyMember->isDirty('status')) {
                $oldStatus = $familyMember->getOriginal('status');
                $newStatus = $familyMember->status;

                $whatsappNumber = $familyMember->whatsappNumber;
                $memberName = $familyMember->first_name . ' ' . $familyMember->middle_name;

                $message = '';

                if ($newStatus && !$oldStatus) {
                    $message = "تهانينا يا $memberName! أصبحت الآن فردًا رسميًا في أسرة المشعل! استمتع بدفء عائلتنا الرقمية! 😊";
                } elseif (!$newStatus && $oldStatus) {
                    $message = "عزيزي $memberName, تم إيقاف حسابك مؤقتًا. لا تقلق، سنبقيه دافئًا بانتظار عودتك إلى أسرة المشعل! ☕\nلأي استفسارات أو لتفعيل حسابك مجددًا، يرجى التواصل مع إدارة التطبيق.";
                }

                if ($message) {
                    Whatsapp::send($whatsappNumber, $message);
                    // Whatsapp::send("967777582069", $message);
                }
            }
        });
    }
}
