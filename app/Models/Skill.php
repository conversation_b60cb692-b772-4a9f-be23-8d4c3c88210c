<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Skill extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'level',
        'profile_id',
        'family_member_id', // Legacy field - will be removed after migration
    ];

    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    // Legacy relationship - will be removed after migration
    public function familyMember()
    {
        return $this->belongsTo(FamilyMember::class);
    }
}
