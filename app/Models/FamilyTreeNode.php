<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use SolutionForest\FilamentTree\Concern\ModelTree;

class FamilyTreeNode extends Model
{
    use HasFactory;
    use SoftDeletes;
    use ModelTree;
    protected $fillable = [
        'name',
        'profile_id',
        'parent_id',
        'order',
        'alive',
        'family_member_id',
    ];

    // Profile relationship (central data)
    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    // Tree structure relationships
    public function children()
    {
        return $this->hasMany(FamilyTreeNode::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(FamilyTreeNode::class, 'parent_id');
    }

    // Optional link to living family member
    public function family_member()
    {
        return $this->belongsTo(FamilyMember::class, 'family_member_id');
    }

    // Connection requests for this node
    public function connectionRequests()
    {
        return $this->hasMany(FamilyTreeConnectionRequest::class, 'family_tree_node_id');
    }

    // Profile-related relationships (delegate to profile)
    public function skills()
    {
        return $this->profile ? $this->profile->skills() : collect();
    }

    public function experiences()
    {
        return $this->profile ? $this->profile->experiences() : collect();
    }

    public function achievements()
    {
        return $this->profile ? $this->profile->achievements() : collect();
    }

    public function comments()
    {
        return $this->profile ? $this->profile->comments() : collect();
    }

    // creating event
    protected static function booted()
    {
        static::creating(function (FamilyTreeNode $familyTreeNode) {
            if (!$familyTreeNode->parent_id) {
                $familyTreeNode->parent_id = -1;
            }
        });
    }
    public function determineTitleColumnName(): string
    {
        return 'name';
    }

    // Delegate to profile (legacy fields removed)
    public function thumbImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->profile
                ? $this->profile->thumb_image_url
                : url('/assets/images/male-avatar.webp')
        );
    }

    public function imageUrl(): Attribute
    {
        return Attribute::make(
            get: fn($value) => $this->profile
                ? $this->profile->image_url
                : url('/assets/images/male-avatar.webp')
        );
    }

    // Name accessor with fallback logic
    public function getDisplayName()
    {
        // Prioritize profile display name when available and not empty
        if ($this->profile && !empty(trim($this->profile->display_name))) {
            return $this->profile->display_name ?? $this->name;
        }

        // Fall back to the name field if it exists and is not empty
        if (!empty($this->attributes['name'])) {
            return $this->attributes['name'];
        }

        // Final fallback
        return 'Unknown';
    }
}
