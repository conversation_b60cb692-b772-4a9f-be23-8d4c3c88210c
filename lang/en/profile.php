<?php
return [
    "plural_label" => "Profiles",
    "singular_label" => "Profile",
    "sections" => [
        "basic_information" => "Basic Information",
        "dates_and_places" => "Dates and Places",
        "description_and_work" => "Description and Work",
        "media" => "Media",
        "social_media_links" => "Social Media Links",
    ],
    "fields" => [
        'first_name' => [
            'label' => 'First Name',
            'placeholder' => 'Enter first name',
        ],
        'middle_name' => [
            'label' => 'Middle Name',
            'placeholder' => 'Enter middle name',
        ],
        'last_name' => [
            'label' => 'Last Name',
            'placeholder' => 'Enter last name',
        ],
        'grandfather_name' => [
            'label' => 'Grandfather Name',
            'placeholder' => 'Enter grandfather name',
        ],
        'nick_name' => [
            'label' => 'Nick Name',
            'placeholder' => 'Enter nick name',
        ],
        'gender' => [
            'label' => 'Gender',
            'placeholder' => 'Select gender',
        ],
        'birth_date' => [
            'label' => 'Birth Date',
            'placeholder' => 'Select birth date',
        ],
        'death_date' => [
            'label' => 'Death Date',
            'placeholder' => 'Select death date',
        ],
        'birth_place' => [
            'label' => 'Birth Place',
            'placeholder' => 'Enter birth place',
        ],
        'death_place' => [
            'label' => 'Death Place',
            'placeholder' => 'Enter death place',
        ],
        'overview' => [
            'label' => 'Overview',
            'placeholder' => 'Enter a brief overview about the person',
        ],
        'job' => [
            'label' => 'Job',
            'placeholder' => 'Enter job title',
        ],
        'address' => [
            'label' => 'Address',
            'placeholder' => 'Enter address',
        ],
        'image' => [
            'label' => 'Profile Image',
            'placeholder' => 'Select profile image',
        ],
        'cover_image' => [
            'label' => 'Cover Image',
            'placeholder' => 'Select cover image',
        ],
        'social_links' => [
            'label' => 'Social Media Links',
            'key_label' => 'Platform',
            'value_label' => 'URL',
        ],
    ],
    "columns" => [
        'id' => 'ID',
        'name' => 'Name',
        'nick_name' => 'Nick Name',
        'gender' => 'Gender',
        'birth_date' => 'Birth Date',
        'death_date' => 'Death Date',
        'job' => 'Job',
        'living_members' => 'Living Members',
        'tree_nodes' => 'Tree Nodes',
        'created' => 'Created',
    ],
    "filters" => [
        'gender' => 'Gender',
        'has_living_members' => 'Has Living Members',
        'deceased' => 'Deceased',
    ],
    "gender_options" => [
        1 => 'Male',
        2 => 'Female',
    ],
    "messages" => [
        "created" => "Profile created successfully",
        "updated" => "Profile updated successfully",
        "deleted" => "Profile deleted successfully",
        "restored" => "Profile restored successfully",
    ],
];
