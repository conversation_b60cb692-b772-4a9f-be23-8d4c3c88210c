<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            // Remove profile-related name and personal info fields (now in Profile model)
            $table->dropColumn([
                'name',
                'nick_name',
                'gender',
                'birth_date',
                'death_date',
                'birth_place',
                'death_place',
                'job',
                'address',
                'overview',
            ]);

            // Remove profile-related media fields
            $table->dropColumn([
                'image',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            // Restore profile-related name and personal info fields
            $table->text('name')->after('profile_id');
            $table->text('nick_name')->nullable()->after('name');
            $table->tinyInteger('gender')->after('order');
            $table->date('birth_date')->nullable()->after('alive');
            $table->date('death_date')->nullable()->after('birth_date');
            $table->text('birth_place')->nullable()->after('death_date');
            $table->text('death_place')->nullable()->after('birth_place');
            $table->text('job')->nullable()->after('death_place');
            $table->text('address')->nullable()->after('job');
            $table->text('overview')->nullable()->after('nick_name');

            // Restore profile-related media fields
            $table->text('image')->nullable()->after('nick_name');
        });
    }
};
