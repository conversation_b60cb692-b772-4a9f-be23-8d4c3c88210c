<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            // Add name field back as a quick identifier for tree nodes
            // This complements the centralized Profile model while providing
            // a simple way to identify nodes in tree operations
            // Check if column doesn't exist to avoid conflicts in test environments
            if (!Schema::hasColumn('family_tree_nodes', 'name')) {
                $table->string('name')->after('profile_id')->index();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('family_tree_nodes', function (Blueprint $table) {
            if (Schema::hasColumn('family_tree_nodes', 'name')) {
                $table->dropIndex(['name']);
                $table->dropColumn('name');
            }
        });
    }
};
