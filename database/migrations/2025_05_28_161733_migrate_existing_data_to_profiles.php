<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Models\Profile;
use App\Models\Skill;
use App\Models\Experience;
use App\Models\Achievement;
use App\Models\Review;
use App\Models\Comment;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->migrateDataToProfiles();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is not easily reversible
        // as it involves data consolidation and deduplication
        throw new Exception('This migration cannot be reversed automatically.');
    }

    private function migrateDataToProfiles(): void
    {
        DB::transaction(function () {
            $this->createProfilesFromFamilyMembers();
            $this->createProfilesFromFamilyTreeNodes();
            $this->updateForeignKeyReferences();
        });
    }

    private function createProfilesFromFamilyMembers(): void
    {
        $familyMembers = FamilyMember::all();

        foreach ($familyMembers as $member) {
            // Create profile from family member data
            $profile = Profile::create([
                'first_name' => $member->first_name,
                'middle_name' => $member->middle_name,
                'last_name' => $member->last_name,
                'grandfather_name' => $member->grandfather_name,
                'nick_name' => null, // FamilyMember doesn't have nick_name
                'gender' => $member->gender,
                'birth_date' => $member->birth_date,
                'death_date' => null, // FamilyMembers are living
                'birth_place' => $member->birth_place,
                'death_place' => null,
                'overview' => $member->overview,
                'image' => $member->image,
                'cover_image' => $member->cover_image,
                'address' => $member->address,
                'job' => null, // FamilyMember doesn't have job field
                'social_links' => [
                    'facebook' => $member->facebook_link,
                    'x' => $member->x_link,
                    'snapshot' => $member->snapshot_link,
                    'youtube' => $member->youtube_link,
                    'linkedin' => $member->linkedin_link,
                    'instagram' => $member->instagram_link,
                ],
                'created_at' => $member->created_at,
                'updated_at' => $member->updated_at,
            ]);

            // Link family member to profile
            $member->update(['profile_id' => $profile->id]);
        }
    }

    private function createProfilesFromFamilyTreeNodes(): void
    {
        // Get tree nodes that don't have a linked family member
        $treeNodes = FamilyTreeNode::whereNull('family_member_id')->get();

        foreach ($treeNodes as $node) {
            // Check if a profile already exists with similar data
            $existingProfile = $this->findSimilarProfile($node);

            if ($existingProfile) {
                // Link to existing profile and merge data if needed
                $this->mergeTreeNodeDataToProfile($existingProfile, $node);
                $node->update(['profile_id' => $existingProfile->id]);
            } else {
                // Create new profile from tree node data
                $profile = Profile::create([
                    'first_name' => $this->extractFirstName($node->name),
                    'middle_name' => $this->extractMiddleName($node->name),
                    'last_name' => $this->extractLastName($node->name),
                    'grandfather_name' => null, // Extract if possible
                    'nick_name' => $node->nick_name,
                    'gender' => $node->gender,
                    'birth_date' => $node->birth_date,
                    'death_date' => $node->death_date,
                    'birth_place' => $node->birth_place,
                    'death_place' => $node->death_place,
                    'overview' => $node->overview,
                    'image' => $node->image,
                    'cover_image' => null,
                    'address' => $node->address,
                    'job' => $node->job,
                    'social_links' => null,
                    'created_at' => $node->created_at,
                    'updated_at' => $node->updated_at,
                ]);

                // Link tree node to profile
                $node->update(['profile_id' => $profile->id]);
            }
        }
    }

    private function findSimilarProfile($node): ?Profile
    {
        // Try to find existing profile with similar name and birth date
        $firstName = $this->extractFirstName($node->name);
        $lastName = $this->extractLastName($node->name);

        return Profile::where('first_name', $firstName)
            ->where('last_name', $lastName)
            ->where('birth_date', $node->birth_date)
            ->first();
    }

    private function mergeTreeNodeDataToProfile(Profile $profile, FamilyTreeNode $node): void
    {
        // Merge additional data from tree node to existing profile
        $updates = [];

        if (!$profile->nick_name && $node->nick_name) {
            $updates['nick_name'] = $node->nick_name;
        }

        if (!$profile->death_date && $node->death_date) {
            $updates['death_date'] = $node->death_date;
        }

        if (!$profile->death_place && $node->death_place) {
            $updates['death_place'] = $node->death_place;
        }

        if (!$profile->job && $node->job) {
            $updates['job'] = $node->job;
        }

        if (!empty($updates)) {
            $profile->update($updates);
        }
    }

    private function updateForeignKeyReferences(): void
    {
        // Update Skills to reference profiles
        $this->updateSkillsReferences();

        // Update Experiences to reference profiles
        $this->updateExperiencesReferences();

        // Update Achievements to reference profiles
        $this->updateAchievementsReferences();

        // Update Reviews to reference profiles
        $this->updateReviewsReferences();

        // Update Comments to reference profiles
        $this->updateCommentsReferences();
    }

    private function updateSkillsReferences(): void
    {
        $skills = Skill::whereNotNull('family_member_id')->get();

        foreach ($skills as $skill) {
            $familyMember = FamilyMember::find($skill->family_member_id);
            if ($familyMember && $familyMember->profile_id) {
                $skill->update(['profile_id' => $familyMember->profile_id]);
            }
        }
    }

    private function updateExperiencesReferences(): void
    {
        $experiences = Experience::whereNotNull('family_member_id')->get();

        foreach ($experiences as $experience) {
            $familyMember = FamilyMember::find($experience->family_member_id);
            if ($familyMember && $familyMember->profile_id) {
                $experience->update(['profile_id' => $familyMember->profile_id]);
            }
        }
    }

    private function updateAchievementsReferences(): void
    {
        $achievements = Achievement::whereNotNull('family_member_id')->get();

        foreach ($achievements as $achievement) {
            $familyMember = FamilyMember::find($achievement->family_member_id);
            if ($familyMember && $familyMember->profile_id) {
                $achievement->update(['profile_id' => $familyMember->profile_id]);
            }
        }
    }

    private function updateReviewsReferences(): void
    {
        $reviews = Review::all();

        foreach ($reviews as $review) {
            $updates = [];

            // Update reviewer reference
            if ($review->reviewer_id) {
                $reviewer = FamilyMember::find($review->reviewer_id);
                if ($reviewer && $reviewer->profile_id) {
                    $updates['reviewer_profile_id'] = $reviewer->profile_id;
                }
            }

            // Update reviewed user reference
            if ($review->reviewed_user_id) {
                $reviewedUser = FamilyMember::find($review->reviewed_user_id);
                if ($reviewedUser && $reviewedUser->profile_id) {
                    $updates['reviewed_profile_id'] = $reviewedUser->profile_id;
                }
            }

            if (!empty($updates)) {
                $review->update($updates);
            }
        }
    }

    private function updateCommentsReferences(): void
    {
        $comments = Comment::whereNotNull('family_member_id')->get();

        foreach ($comments as $comment) {
            $familyMember = FamilyMember::find($comment->family_member_id);
            if ($familyMember && $familyMember->profile_id) {
                $comment->update(['profile_id' => $familyMember->profile_id]);
            }
        }
    }

    // Helper methods to extract name parts from FamilyTreeNode name field
    private function extractFirstName($fullName): ?string
    {
        if (!$fullName) return null;
        $parts = explode(' ', trim($fullName));
        return $parts[0] ?? null;
    }

    private function extractMiddleName($fullName): ?string
    {
        if (!$fullName) return null;
        $parts = explode(' ', trim($fullName));
        return isset($parts[1]) && count($parts) > 2 ? $parts[1] : null;
    }

    private function extractLastName($fullName): ?string
    {
        if (!$fullName) return null;
        $parts = explode(' ', trim($fullName));
        return count($parts) > 1 ? end($parts) : null;
    }
};
