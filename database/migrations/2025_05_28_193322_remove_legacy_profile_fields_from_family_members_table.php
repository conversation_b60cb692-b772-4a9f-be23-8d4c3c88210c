<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('family_members', function (Blueprint $table) {
            // Remove profile-related name fields (now in Profile model)
            $table->dropColumn([
                'first_name',
                'middle_name',
                'last_name',
                'grandfather_name',
            ]);

            // Remove profile-related personal info fields
            $table->dropColumn([
                'gender',
                'birth_date',
                'birth_place',
                'address',
                'overview',
            ]);

            // Remove profile-related media fields
            $table->dropColumn([
                'image',
                'cover_image',
            ]);

            // Remove profile-related social media fields
            $table->dropColumn([
                'facebook_link',
                'x_link',
                'snapshot_link',
                'youtube_link',
                'linkedin_link',
                'instagram_link',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('family_members', function (Blueprint $table) {
            // Restore profile-related name fields
            $table->string('first_name')->nullable()->after('profile_id');
            $table->string('middle_name')->nullable()->after('first_name');
            $table->string('last_name')->nullable()->after('middle_name');
            $table->string('grandfather_name')->nullable()->after('last_name');

            // Restore profile-related personal info fields
            $table->tinyInteger('gender')->nullable()->after('branch_id');
            $table->date('birth_date')->nullable()->after('image');
            $table->string('birth_place')->nullable()->after('birth_date');
            $table->string('address')->nullable()->after('city_id');
            $table->text('overview')->nullable()->after('grandfather_name');

            // Restore profile-related media fields
            $table->string('image')->nullable()->after('password');
            $table->string('cover_image')->nullable()->after('image');

            // Restore profile-related social media fields
            $table->string('facebook_link')->nullable()->after('cover_image');
            $table->string('x_link')->nullable()->after('facebook_link');
            $table->string('snapshot_link')->nullable()->after('x_link');
            $table->string('youtube_link')->nullable()->after('snapshot_link');
            $table->string('linkedin_link')->nullable()->after('youtube_link');
            $table->string('instagram_link')->nullable()->after('linkedin_link');
        });
    }
};
