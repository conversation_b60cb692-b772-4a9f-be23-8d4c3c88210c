<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('excellence_awards', function (Blueprint $table) {
            $table->string('type', 50)->default('academic_excellence')->after('content');
        });

        // Set default value for existing records
        DB::statement("UPDATE excellence_awards SET type = 'academic_excellence' WHERE type IS NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('excellence_awards', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
