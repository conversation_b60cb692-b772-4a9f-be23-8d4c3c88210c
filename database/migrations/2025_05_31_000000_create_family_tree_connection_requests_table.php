<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('family_tree_connection_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('family_member_id')
                ->constrained()
                ->cascadeOnDelete();
            $table->foreignId('family_tree_node_id')
                ->constrained()
                ->cascadeOnDelete();
            $table->enum('status', ['pending', 'approved', 'rejected'])
                ->default('pending');
            $table->text('note')->nullable();
            $table->timestamp('requested_at')->useCurrent();
            $table->timestamp('reviewed_at')->nullable();
            $table->foreignId('reviewed_by')
                ->nullable()
                ->constrained('users')
                ->nullOnDelete();
            $table->timestamps();

            // Prevent duplicate requests
            $table->unique(['family_member_id', 'family_tree_node_id'], 'unique_connection_request');
            
            // Indexes for performance
            $table->index(['status', 'requested_at']);
            $table->index(['family_tree_node_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('family_tree_connection_requests');
    }
};
