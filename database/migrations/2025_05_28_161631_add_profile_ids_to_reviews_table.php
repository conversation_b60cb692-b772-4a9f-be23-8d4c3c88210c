<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->foreignId('reviewer_profile_id')->nullable()->after('id')->constrained('profiles')->onDelete('cascade');
            $table->foreignId('reviewed_profile_id')->nullable()->after('reviewer_profile_id')->constrained('profiles')->onDelete('cascade');
            $table->index('reviewer_profile_id');
            $table->index('reviewed_profile_id');
            $table->index(['reviewed_profile_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropForeign(['reviewer_profile_id']);
            $table->dropForeign(['reviewed_profile_id']);
            $table->dropIndex(['reviewer_profile_id']);
            $table->dropIndex(['reviewed_profile_id']);
            $table->dropIndex(['reviewed_profile_id', 'status']);
            $table->dropColumn(['reviewer_profile_id', 'reviewed_profile_id']);
        });
    }
};
