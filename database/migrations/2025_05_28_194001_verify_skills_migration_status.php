<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Skill;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->verifySkillsMigrationStatus();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to reverse for verification
    }
    
    /**
     * Verify the current status of skills migration
     */
    private function verifySkillsMigrationStatus(): void
    {
        $this->command->info("🔍 Verifying Skills Migration Status...");
        
        // Total skills count
        $totalSkills = Skill::count();
        $this->command->info("📊 Total skills in database: {$totalSkills}");
        
        if ($totalSkills === 0) {
            $this->command->info("✅ No skills found in database - migration cleanup can proceed safely.");
            return;
        }
        
        // Skills with profile_id
        $skillsWithProfile = Skill::whereNotNull('profile_id')->count();
        $this->command->info("✅ Skills with profile_id: {$skillsWithProfile}");
        
        // Skills with family_member_id but no profile_id (unmigrated)
        $unmigratedSkills = Skill::whereNull('profile_id')->whereNotNull('family_member_id')->count();
        if ($unmigratedSkills > 0) {
            $this->command->error("❌ Unmigrated skills (have family_member_id but no profile_id): {$unmigratedSkills}");
            $this->command->error("   These need to be migrated before removing the legacy column.");
        } else {
            $this->command->info("✅ No unmigrated skills found.");
        }
        
        // Orphaned skills (no profile_id and no family_member_id)
        $orphanedSkills = Skill::whereNull('profile_id')->whereNull('family_member_id')->count();
        if ($orphanedSkills > 0) {
            $this->command->warn("⚠️  Orphaned skills (no profile_id and no family_member_id): {$orphanedSkills}");
            $this->command->warn("   These records need to be handled before cleanup.");
        } else {
            $this->command->info("✅ No orphaned skills found.");
        }
        
        // Skills with both profile_id and family_member_id (should be cleaned up)
        $duplicateReferences = Skill::whereNotNull('profile_id')->whereNotNull('family_member_id')->count();
        if ($duplicateReferences > 0) {
            $this->command->info("🔄 Skills with both profile_id and family_member_id: {$duplicateReferences}");
            $this->command->info("   These are properly migrated and ready for legacy column cleanup.");
        }
        
        // Summary
        $this->command->info("\n📋 Migration Status Summary:");
        $this->command->info("   Total Skills: {$totalSkills}");
        $this->command->info("   ✅ Migrated: {$skillsWithProfile}");
        $this->command->info("   ❌ Unmigrated: {$unmigratedSkills}");
        $this->command->info("   ⚠️  Orphaned: {$orphanedSkills}");
        
        if ($unmigratedSkills === 0 && $orphanedSkills === 0) {
            $this->command->info("\n🎉 All skills are properly migrated! Ready for legacy column cleanup.");
        } else {
            $this->command->error("\n🚫 Migration issues found. Please resolve before proceeding with cleanup.");
        }
    }
};
