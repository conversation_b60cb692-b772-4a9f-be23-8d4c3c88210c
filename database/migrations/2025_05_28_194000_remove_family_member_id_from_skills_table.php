<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Skill;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, verify that all skills have been migrated to use profile_id
        $this->verifyDataMigration();
        
        // Remove the legacy family_member_id column and its constraints
        Schema::table('skills', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['family_member_id']);
            
            // Drop the column
            $table->dropColumn('family_member_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-add the family_member_id column for rollback
        Schema::table('skills', function (Blueprint $table) {
            $table->foreignId('family_member_id')->nullable()->after('level')->constrained()->onDelete('cascade');
        });
    }
    
    /**
     * Verify that all skills have been properly migrated to use profile_id
     */
    private function verifyDataMigration(): void
    {
        // Check if there are any skills without profile_id but with family_member_id
        $unmigrated = Skill::whereNull('profile_id')->whereNotNull('family_member_id')->count();
        
        if ($unmigrated > 0) {
            throw new \Exception(
                "Cannot remove family_member_id column: {$unmigrated} skills still need to be migrated to use profile_id. " .
                "Please run the data migration first: php artisan migrate --path=database/migrations/2025_05_28_161733_migrate_existing_data_to_profiles.php"
            );
        }
        
        // Check if there are any skills without profile_id at all
        $orphaned = Skill::whereNull('profile_id')->count();
        
        if ($orphaned > 0) {
            throw new \Exception(
                "Cannot remove family_member_id column: {$orphaned} skills have no profile_id assigned. " .
                "These orphaned records need to be handled before removing the legacy column."
            );
        }
        
        $this->command->info("✓ Data migration verification passed: All skills are properly linked to profiles.");
    }
};
