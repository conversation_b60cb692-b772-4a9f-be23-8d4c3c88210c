<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('profiles', function (Blueprint $table) {
            $table->id();

            // Name fields
            $table->string('first_name')->nullable();
            $table->string('middle_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('grandfather_name')->nullable();
            $table->string('nick_name')->nullable();

            // Basic info
            $table->tinyInteger('gender')->nullable();

            // Dates and places
            $table->date('birth_date')->nullable();
            $table->date('death_date')->nullable();
            $table->text('birth_place')->nullable();
            $table->text('death_place')->nullable();

            // Description and media
            $table->text('overview')->nullable();
            $table->text('image')->nullable();
            $table->string('cover_image')->nullable();

            // Location and work
            $table->text('address')->nullable();
            $table->text('job')->nullable();

            // Social media links (JSON)
            $table->json('social_links')->nullable();

            $table->softDeletes();
            $table->timestamps();

            // Indexes for performance
            $table->index(['first_name', 'middle_name', 'last_name']);
            $table->index('birth_date');
            $table->index('death_date');
            $table->index('gender');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('profiles');
    }
};
