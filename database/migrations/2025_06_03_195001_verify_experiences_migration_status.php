<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Experience;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->verifyExperiencesMigrationStatus();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to reverse for verification
    }
    
    /**
     * Verify the current status of experiences migration
     */
    private function verifyExperiencesMigrationStatus(): void
    {
        echo "🔍 Verifying Experiences Migration Status...\n";
        
        // Total experiences count
        $totalExperiences = Experience::count();
        echo "📊 Total experiences in database: {$totalExperiences}\n";
        
        if ($totalExperiences === 0) {
            echo "✅ No experiences found in database - migration cleanup can proceed safely.\n";
            return;
        }
        
        // Experiences with profile_id
        $experiencesWithProfile = Experience::whereNotNull('profile_id')->count();
        echo "✅ Experiences with profile_id: {$experiencesWithProfile}\n";
        
        // Experiences with family_member_id but no profile_id (unmigrated)
        $unmigratedExperiences = Experience::whereNull('profile_id')->whereNotNull('family_member_id')->count();
        if ($unmigratedExperiences > 0) {
            echo "❌ Unmigrated experiences (have family_member_id but no profile_id): {$unmigratedExperiences}\n";
            echo "   These need to be migrated before removing the legacy column.\n";
        } else {
            echo "✅ No unmigrated experiences found.\n";
        }
        
        // Orphaned experiences (no profile_id and no family_member_id)
        $orphanedExperiences = Experience::whereNull('profile_id')->whereNull('family_member_id')->count();
        if ($orphanedExperiences > 0) {
            echo "⚠️  Orphaned experiences (no profile_id and no family_member_id): {$orphanedExperiences}\n";
            echo "   These records need to be handled before cleanup.\n";
        } else {
            echo "✅ No orphaned experiences found.\n";
        }
        
        // Experiences with both profile_id and family_member_id (should be cleaned up)
        $duplicateReferences = Experience::whereNotNull('profile_id')->whereNotNull('family_member_id')->count();
        if ($duplicateReferences > 0) {
            echo "🔄 Experiences with both profile_id and family_member_id: {$duplicateReferences}\n";
            echo "   These are properly migrated and ready for legacy column cleanup.\n";
        }
        
        // Summary
        echo "\n📋 Migration Status Summary:\n";
        echo "   Total Experiences: {$totalExperiences}\n";
        echo "   ✅ Migrated: {$experiencesWithProfile}\n";
        echo "   ❌ Unmigrated: {$unmigratedExperiences}\n";
        echo "   ⚠️  Orphaned: {$orphanedExperiences}\n";
        
        if ($unmigratedExperiences === 0 && $orphanedExperiences === 0) {
            echo "\n🎉 All experiences are properly migrated! Ready for legacy column cleanup.\n";
        } else {
            echo "\n🚫 Migration issues found. Please resolve before proceeding with cleanup.\n";
        }
    }
};
