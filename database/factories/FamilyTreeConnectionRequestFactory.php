<?php

namespace Database\Factories;

use App\Models\FamilyTreeConnectionRequest;
use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FamilyTreeConnectionRequest>
 */
class FamilyTreeConnectionRequestFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FamilyTreeConnectionRequest::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'family_member_id' => FamilyMember::factory(),
            'family_tree_node_id' => FamilyTreeNode::factory(),
            'status' => FamilyTreeConnectionRequest::STATUS_PENDING,
            'note' => $this->faker->paragraph(),
            'requested_at' => now(),
            'reviewed_at' => null,
            'reviewed_by' => null,
        ];
    }

    /**
     * Indicate that the request is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => FamilyTreeConnectionRequest::STATUS_PENDING,
            'reviewed_at' => null,
            'reviewed_by' => null,
        ]);
    }

    /**
     * Indicate that the request is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => FamilyTreeConnectionRequest::STATUS_APPROVED,
            'reviewed_at' => now(),
            'reviewed_by' => 1, // Assuming admin user ID 1
        ]);
    }

    /**
     * Indicate that the request is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => FamilyTreeConnectionRequest::STATUS_REJECTED,
            'reviewed_at' => now(),
            'reviewed_by' => 1, // Assuming admin user ID 1
        ]);
    }
}
