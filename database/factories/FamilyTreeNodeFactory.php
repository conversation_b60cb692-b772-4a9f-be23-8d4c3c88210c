<?php

namespace Database\Factories;

use App\Models\FamilyTreeNode;
use App\Models\Profile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FamilyTreeNode>
 */
class FamilyTreeNodeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FamilyTreeNode::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'profile_id' => Profile::factory(),
            'parent_id' => null,
            'order' => $this->faker->numberBetween(1, 100),
            'alive' => $this->faker->boolean(80), // 80% chance of being alive
            'family_member_id' => null, // Usually not linked to a family member
        ];
    }

    /**
     * Indicate that the tree node is for a living person.
     */
    public function alive(): static
    {
        return $this->state(fn(array $attributes) => [
            'alive' => true,
        ]);
    }

    /**
     * Indicate that the tree node is for a deceased person.
     */
    public function deceased(): static
    {
        return $this->state(fn(array $attributes) => [
            'alive' => false,
        ]);
    }

    /**
     * Set a specific parent for this node.
     */
    public function withParent(FamilyTreeNode $parent): static
    {
        return $this->state(fn(array $attributes) => [
            'parent_id' => $parent->id,
        ]);
    }

    /**
     * Make this a root node.
     */
    public function root(): static
    {
        return $this->state(fn(array $attributes) => [
            'parent_id' => -1,
        ]);
    }
}
