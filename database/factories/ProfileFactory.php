<?php

namespace Database\Factories;

use App\Models\Profile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Profile>
 */
class ProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Profile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'middle_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'grandfather_name' => $this->faker->lastName(),
            'nick_name' => $this->faker->optional(0.3)->name(),
            'gender' => $this->faker->randomElement([1, 2]), // 1: male, 2: female
            'birth_date' => $this->faker->date(),
            'death_date' => $this->faker->optional(0.2)->date(),
            'birth_place' => $this->faker->city(),
            'death_place' => $this->faker->optional(0.2)->city(),
            'overview' => $this->faker->optional(0.7)->paragraph(),
            'image' => null,
            'cover_image' => null,
            'address' => $this->faker->address(),
            'job' => $this->faker->optional(0.8)->jobTitle(),
            'social_links' => $this->faker->optional(0.3)->randomElement([
                [
                    'facebook' => 'https://facebook.com/user',
                    'twitter' => 'https://twitter.com/user',
                ],
                [
                    'linkedin' => 'https://linkedin.com/in/user',
                ],
                []
            ]),
        ];
    }

    /**
     * Indicate that the profile is for a male.
     */
    public function male(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => 1,
        ]);
    }

    /**
     * Indicate that the profile is for a female.
     */
    public function female(): static
    {
        return $this->state(fn (array $attributes) => [
            'gender' => 2,
        ]);
    }

    /**
     * Indicate that the profile is for a deceased person.
     */
    public function deceased(): static
    {
        return $this->state(fn (array $attributes) => [
            'death_date' => $this->faker->date(),
            'death_place' => $this->faker->city(),
        ]);
    }

    /**
     * Indicate that the profile is for a living person.
     */
    public function alive(): static
    {
        return $this->state(fn (array $attributes) => [
            'death_date' => null,
            'death_place' => null,
        ]);
    }
}
