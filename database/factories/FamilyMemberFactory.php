<?php

namespace Database\Factories;

use App\Models\FamilyMember;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\FamilyMember>
 */
class FamilyMemberFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = FamilyMember::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'profile_id' => \App\Models\Profile::factory(),
            'email' => $this->faker->unique()->safeEmail(),
            'mobile' => $this->faker->numerify('#########'),
            'branch_id' => null,
            'password' => Hash::make('password'),
            'status' => true,
            'reset_password_code' => null,
            'country_id' => null,
            'city_id' => null,
            'cv_file' => null,
            'cv_type' => null,
            'cv_text' => null,
            'country_code' => '+966',
            'family_tree_node_id' => null,
        ];
    }

    /**
     * Indicate that the family member is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the family member is male.
     */
    public function male(): static
    {
        return $this->state(fn(array $attributes) => [
            'profile_id' => \App\Models\Profile::factory()->male(),
        ]);
    }

    /**
     * Indicate that the family member is female.
     */
    public function female(): static
    {
        return $this->state(fn(array $attributes) => [
            'profile_id' => \App\Models\Profile::factory()->female(),
        ]);
    }
}
