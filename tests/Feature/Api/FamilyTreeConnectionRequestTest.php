<?php

namespace Tests\Feature\Api;

use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Models\FamilyTreeConnectionRequest;
use App\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\RateLimiter;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class FamilyTreeConnectionRequestTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Clear rate limiter for tests
        RateLimiter::clear('connection-request:1');
    }

    /**
     * Get headers with API key for testing
     */
    private function getApiHeaders(): array
    {
        return [
            'api-key' => 'NwaAi8q5SXQAu9P5X3bqSPGkakoI'
        ];
    }

    /** @test */
    public function authenticated_user_can_create_connection_request()
    {
        // Create a family member with profile
        $profile = Profile::factory()->create();
        $familyMember = FamilyMember::factory()->create([
            'profile_id' => $profile->id,
            'family_tree_node_id' => null, // Not connected to any node
        ]);

        // Create a family tree node with profile
        $nodeProfile = Profile::factory()->create();
        $familyTreeNode = FamilyTreeNode::factory()->create([
            'profile_id' => $nodeProfile->id,
        ]);

        Sanctum::actingAs($familyMember);

        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNode->id}/connection-requests", [
            'note' => 'This is my grandfather and I would like to connect my profile to his.',
        ], $this->getApiHeaders());

        $response->assertStatus(201)
            ->assertJson([
                'message' => __('family_tree_connection_request.messages.request_created'),
                'data' => [
                    'family_member_id' => $familyMember->id,
                    'family_tree_node_id' => $familyTreeNode->id,
                    'status' => 'pending',
                ],
            ]);

        $this->assertDatabaseHas('family_tree_connection_requests', [
            'family_member_id' => $familyMember->id,
            'family_tree_node_id' => $familyTreeNode->id,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_create_connection_request()
    {
        $familyTreeNode = FamilyTreeNode::factory()->create();

        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNode->id}/connection-requests", [
            'note' => 'This is my grandfather.',
        ], $this->getApiHeaders());

        $response->assertStatus(401);
    }

    /** @test */
    public function user_cannot_create_request_for_nonexistent_node()
    {
        $familyMember = FamilyMember::factory()->create();

        Sanctum::actingAs($familyMember);

        $response = $this->postJson("/api/v1/family-tree-nodes/999999/connection-requests", [
            'note' => 'This is my grandfather.',
        ], $this->getApiHeaders());

        $response->assertStatus(404)
            ->assertJson([
                'message' => __('family_tree_connection_request.messages.node_not_found'),
            ]);
    }

    /** @test */
    public function user_cannot_create_request_if_already_connected()
    {
        $familyTreeNode = FamilyTreeNode::factory()->create();
        $familyMember = FamilyMember::factory()->create([
            'family_tree_node_id' => $familyTreeNode->id, // Already connected
        ]);

        $anotherNode = FamilyTreeNode::factory()->create();

        Sanctum::actingAs($familyMember);

        $response = $this->postJson("/api/v1/family-tree-nodes/{$anotherNode->id}/connection-requests", [
            'note' => 'This is my grandfather.',
        ], $this->getApiHeaders());

        $response->assertStatus(409)
            ->assertJson([
                'message' => __('family_tree_connection_request.messages.already_connected'),
            ]);
    }

    /** @test */
    public function user_cannot_create_duplicate_request()
    {
        $familyMember = FamilyMember::factory()->create();
        $familyTreeNode = FamilyTreeNode::factory()->create();

        // Create existing pending request
        FamilyTreeConnectionRequest::factory()->create([
            'family_member_id' => $familyMember->id,
            'family_tree_node_id' => $familyTreeNode->id,
            'status' => 'pending',
        ]);

        Sanctum::actingAs($familyMember);

        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNode->id}/connection-requests", [
            'note' => 'This is my grandfather.',
        ], $this->getApiHeaders());

        $response->assertStatus(409)
            ->assertJson([
                'message' => __('family_tree_connection_request.messages.duplicate_request'),
            ]);
    }

    /** @test */
    public function request_requires_valid_note()
    {
        $familyMember = FamilyMember::factory()->create();
        $familyTreeNode = FamilyTreeNode::factory()->create();

        Sanctum::actingAs($familyMember);

        // Test missing note
        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNode->id}/connection-requests", [], $this->getApiHeaders());

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['note']);

        // Test note too short
        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNode->id}/connection-requests", [
            'note' => 'short',
        ], $this->getApiHeaders());

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['note']);

        // Test note too long
        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNode->id}/connection-requests", [
            'note' => str_repeat('a', 501),
        ], $this->getApiHeaders());

        $response->assertStatus(400)
            ->assertJsonValidationErrors(['note']);
    }

    /** @test */
    public function rate_limiting_works()
    {
        $familyMember = FamilyMember::factory()->create();
        $familyTreeNodes = FamilyTreeNode::factory()->count(4)->create();

        Sanctum::actingAs($familyMember);

        // Make 3 requests (should work)
        for ($i = 0; $i < 3; $i++) {
            $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNodes[$i]->id}/connection-requests", [
                'note' => "This is my relative number {$i}.",
            ], $this->getApiHeaders());

            $response->assertStatus(201);
        }

        // 4th request should be rate limited
        $response = $this->postJson("/api/v1/family-tree-nodes/{$familyTreeNodes[3]->id}/connection-requests", [
            'note' => 'This should be rate limited.',
        ], $this->getApiHeaders());

        $response->assertStatus(429)
            ->assertJson([
                'message' => __('family_tree_connection_request.messages.rate_limit_exceeded'),
            ]);
    }
}
