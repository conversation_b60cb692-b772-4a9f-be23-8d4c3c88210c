<?php

namespace Tests\Feature;

use App\Enums\ExcellenceAwardType;
use App\Models\ExcellenceAward;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ExcellenceAwardTypeTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test that ExcellenceAward can be created with type field.
     */
    public function test_excellence_award_can_be_created_with_type(): void
    {
        $award = ExcellenceAward::factory()->create([
            'type' => ExcellenceAwardType::AcademicExcellence->value,
        ]);

        $this->assertDatabaseHas('excellence_awards', [
            'id' => $award->id,
            'type' => 'academic_excellence',
        ]);

        $this->assertEquals(ExcellenceAwardType::AcademicExcellence, $award->type);
    }

    /**
     * Test that ExcellenceAward type enum casting works correctly.
     */
    public function test_excellence_award_type_enum_casting(): void
    {
        $academicAward = ExcellenceAward::factory()->academicExcellence()->create();
        $quranAward = ExcellenceAward::factory()->quranMemorization()->create();

        $this->assertEquals(ExcellenceAwardType::AcademicExcellence, $academicAward->type);
        $this->assertEquals(ExcellenceAwardType::QuranMemorization, $quranAward->type);

        $this->assertEquals('academic_excellence', $academicAward->type->value);
        $this->assertEquals('quran_memorization', $quranAward->type->value);
    }

    /**
     * Test ExcellenceAward scopes work correctly.
     */
    public function test_excellence_award_scopes(): void
    {
        ExcellenceAward::factory()->academicExcellence()->count(3)->create();
        ExcellenceAward::factory()->quranMemorization()->count(2)->create();

        $this->assertEquals(3, ExcellenceAward::academicExcellence()->count());
        $this->assertEquals(2, ExcellenceAward::quranMemorization()->count());
        $this->assertEquals(5, ExcellenceAward::count());
    }

    /**
     * Test ExcellenceAward API resource includes type field.
     */
    public function test_api_resource_includes_type_field(): void
    {
        $award = ExcellenceAward::factory()->academicExcellence()->create();

        $resource = new \App\Http\Resources\Api\ExcellenceAwardJsonResource($award);
        $array = $resource->toArray(request());

        $this->assertArrayHasKey('type', $array);
        $this->assertEquals(__('excellence_award.types.academic_excellence'), $array['type']);
        $this->assertArrayHasKey('type_key', $array);
        $this->assertEquals('academic_excellence', $array['type_key']);
    }

    /**
     * Test ExcellenceAwardType enum options.
     */
    public function test_excellence_award_type_enum_options(): void
    {
        $options = ExcellenceAwardType::getOptions();

        $this->assertArrayHasKey('academic_excellence', $options);
        $this->assertArrayHasKey('quran_memorization', $options);
        $this->assertCount(2, $options);

        // Test that the options contain Arabic labels
        $this->assertEquals(__('excellence_award.types.academic_excellence'), $options['academic_excellence']);
        $this->assertEquals(__('excellence_award.types.quran_memorization'), $options['quran_memorization']);
    }

    /**
     * Test ExcellenceAwardType enum implements Filament contracts.
     */
    public function test_excellence_award_type_enum_contracts(): void
    {
        $academicType = ExcellenceAwardType::AcademicExcellence;
        $quranType = ExcellenceAwardType::QuranMemorization;

        // Test HasLabel contract
        $this->assertEquals(__('excellence_award.types.academic_excellence'), $academicType->getLabel());
        $this->assertEquals(__('excellence_award.types.quran_memorization'), $quranType->getLabel());

        // Test HasColor contract
        $this->assertEquals('success', $academicType->getColor());
        $this->assertEquals('info', $quranType->getColor());

        // Test HasIcon contract
        $this->assertEquals('heroicon-m-academic-cap', $academicType->getIcon());
        $this->assertEquals('heroicon-m-book-open', $quranType->getIcon());

        // Test HasDescription contract
        $this->assertEquals(__('excellence_award.types.academic_excellence_description'), $academicType->getDescription());
        $this->assertEquals(__('excellence_award.types.quran_memorization_description'), $quranType->getDescription());
    }

    /**
     * Test API controller backward compatibility for type filtering.
     */
    public function test_api_controller_backward_compatibility(): void
    {
        $controller = new \App\Http\Controllers\Api\ExcellenceAwardController();
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('normalizeTypeValue');
        $method->setAccessible(true);

        // Test English values pass through unchanged
        $this->assertEquals('academic_excellence', $method->invoke($controller, 'academic_excellence'));
        $this->assertEquals('quran_memorization', $method->invoke($controller, 'quran_memorization'));

        // Test Arabic values are converted to English
        $this->assertEquals('academic_excellence', $method->invoke($controller, 'التفوق الدراسي'));
        $this->assertEquals('quran_memorization', $method->invoke($controller, 'جوائز حفظ القرآن'));

        // Test unknown values pass through unchanged
        $this->assertEquals('unknown_type', $method->invoke($controller, 'unknown_type'));
    }
}
