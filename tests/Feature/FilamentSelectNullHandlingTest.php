<?php

namespace Tests\Feature;

use App\Models\FamilyMember;
use App\Models\FamilyTreeNode;
use App\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FilamentSelectNullHandlingTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function family_member_select_options_handle_null_display_names()
    {
        // Create a profile with null/empty name fields
        $profile = Profile::factory()->create([
            'first_name' => null,
            'middle_name' => null,
            'last_name' => null,
        ]);

        $familyMember = FamilyMember::factory()->create([
            'profile_id' => $profile->id,
            'email' => '<EMAIL>',
        ]);

        // Test the logic from our Filament resource
        $displayName = $familyMember->profile?->display_name;
        $label = (!empty(trim($displayName))) ? $displayName : ($familyMember->email ?? 'Unknown Member');

        $this->assertEquals('<EMAIL>', $label);
    }

    /** @test */
    public function family_member_select_options_handle_empty_display_names()
    {
        // Create a profile with empty string name fields
        $profile = Profile::factory()->create([
            'first_name' => '',
            'middle_name' => '',
            'last_name' => '',
        ]);

        $familyMember = FamilyMember::factory()->create([
            'profile_id' => $profile->id,
            'email' => '<EMAIL>',
        ]);

        // Test the logic from our Filament resource
        $displayName = $familyMember->profile?->display_name;
        $label = (!empty(trim($displayName))) ? $displayName : ($familyMember->email ?? 'Unknown Member');

        $this->assertEquals('<EMAIL>', $label);
    }

    /** @test */
    public function family_member_select_options_use_display_name_when_available()
    {
        // Create a profile with proper name fields
        $profile = Profile::factory()->create([
            'first_name' => 'John',
            'middle_name' => 'William',
            'last_name' => 'Doe',
        ]);

        $familyMember = FamilyMember::factory()->create([
            'profile_id' => $profile->id,
            'email' => '<EMAIL>',
        ]);

        // Test the logic from our Filament resource
        $displayName = $familyMember->profile?->display_name;
        $label = (!empty(trim($displayName))) ? $displayName : ($familyMember->email ?? 'Unknown Member');

        $this->assertEquals('John William Doe', $label);
    }

    /** @test */
    public function family_tree_node_select_options_handle_null_display_names()
    {
        // Create a profile with null name fields
        $profile = Profile::factory()->create([
            'first_name' => null,
            'middle_name' => null,
            'last_name' => null,
        ]);

        $familyTreeNode = FamilyTreeNode::factory()->create([
            'profile_id' => $profile->id,
        ]);

        // Test the logic from our Filament resource
        $displayName = $familyTreeNode->profile?->display_name;
        $label = (!empty(trim($displayName))) ? $displayName : 'Unknown Node #' . $familyTreeNode->id;

        $this->assertEquals('Unknown Node #' . $familyTreeNode->id, $label);
    }

    /** @test */
    public function family_tree_node_select_options_handle_no_profile()
    {
        // Create a node without a profile
        $familyTreeNode = FamilyTreeNode::factory()->create([
            'profile_id' => null,
        ]);

        // Test the logic from our Filament resource
        $displayName = $familyTreeNode->profile?->display_name;
        $label = (!empty(trim($displayName))) ? $displayName : 'Unknown Node #' . $familyTreeNode->id;

        $this->assertEquals('Unknown Node #' . $familyTreeNode->id, $label);
    }
}
