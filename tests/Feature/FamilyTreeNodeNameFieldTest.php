<?php

namespace Tests\Feature;

use App\Models\FamilyTreeNode;
use App\Models\Profile;
use App\Http\Resources\Api\FamilyTreeNodeJsonResource;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FamilyTreeNodeNameFieldTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function family_tree_node_can_be_created_with_name_field()
    {
        $profile = Profile::factory()->create([
            'first_name' => '<PERSON>',
            'middle_name' => '<PERSON>',
            'last_name' => 'Do<PERSON>',
        ]);

        $node = FamilyTreeNode::factory()->create([
            'name' => '<PERSON> (Tree Node)',
            'profile_id' => $profile->id,
        ]);

        $this->assertDatabaseHas('family_tree_nodes', [
            'id' => $node->id,
            'name' => '<PERSON> (Tree Node)',
            'profile_id' => $profile->id,
        ]);

        $this->assertEquals('<PERSON> (Tree Node)', $node->name);
        $this->assertEquals('<PERSON>', $node->profile->display_name);
    }

    /** @test */
    public function family_tree_node_can_be_created_without_profile()
    {
        $node = FamilyTreeNode::factory()->create([
            'name' => 'Historical Figure',
            'profile_id' => null,
        ]);

        $this->assertDatabaseHas('family_tree_nodes', [
            'id' => $node->id,
            'name' => 'Historical Figure',
            'profile_id' => null,
        ]);

        $this->assertEquals('Historical Figure', $node->name);
        $this->assertNull($node->profile);
    }

    /** @test */
    public function api_resource_prioritizes_profile_display_name_over_node_name()
    {
        $profile = Profile::factory()->create([
            'first_name' => 'Jane',
            'middle_name' => 'Marie',
            'last_name' => 'Smith',
        ]);

        $node = FamilyTreeNode::factory()->create([
            'name' => 'Old Name',
            'profile_id' => $profile->id,
        ]);

        $resource = new FamilyTreeNodeJsonResource($node);
        $resourceArray = $resource->toArray(request());

        // Should use profile display name, not the node name
        $this->assertEquals('Jane Marie Smith', $resourceArray['name']);
        $this->assertEquals($profile->id, $resourceArray['profile_id']);
    }

    /** @test */
    public function api_resource_falls_back_to_node_name_when_no_profile()
    {
        $node = FamilyTreeNode::factory()->create([
            'name' => 'Historical Person',
            'profile_id' => null,
        ]);

        $resource = new FamilyTreeNodeJsonResource($node);
        $resourceArray = $resource->toArray(request());

        // Should use node name when no profile
        $this->assertEquals('Historical Person', $resourceArray['name']);
        $this->assertNull($resourceArray['profile_id']);
    }

    /** @test */
    public function api_resource_falls_back_to_node_name_when_profile_has_empty_display_name()
    {
        $profile = Profile::factory()->create([
            'first_name' => null,
            'middle_name' => null,
            'last_name' => null,
        ]);

        $node = FamilyTreeNode::factory()->create([
            'name' => 'Fallback Name',
            'profile_id' => $profile->id,
        ]);

        $resource = new FamilyTreeNodeJsonResource($node);
        $resourceArray = $resource->toArray(request());

        // Should fall back to node name when profile display name is empty
        $this->assertEquals('Fallback Name', $resourceArray['name']);
        $this->assertEquals($profile->id, $resourceArray['profile_id']);
    }

    /** @test */
    public function name_field_is_fillable()
    {
        $node = new FamilyTreeNode();
        $fillable = $node->getFillable();

        $this->assertContains('name', $fillable);
    }

    /** @test */
    public function name_field_is_required_for_tree_operations()
    {
        // Test that the name field works with tree operations
        $parentNode = FamilyTreeNode::factory()->create([
            'name' => 'Parent Node',
            'parent_id' => -1, // Root node
        ]);

        $childNode = FamilyTreeNode::factory()->create([
            'name' => 'Child Node',
            'parent_id' => $parentNode->id,
        ]);

        $this->assertEquals('Parent Node', $parentNode->name);
        $this->assertEquals('Child Node', $childNode->name);
        $this->assertEquals($parentNode->id, $childNode->parent_id);
        
        // Test relationship
        $this->assertTrue($parentNode->children->contains($childNode));
        $this->assertEquals($parentNode->id, $childNode->parent->id);
    }
}
