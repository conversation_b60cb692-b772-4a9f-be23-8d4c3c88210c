<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/app/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="تطبيق أسرة المشعل - منصة تواصل أفراد العائلة">
  <meta name="keywords" content="أسرة المشعل, عائلة, تواصل, شجرة العائلة">
  <meta name="author" content="Almashal Family">
  <meta name="robots" content="index, follow">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="أسرة المشعل - Almashal Family">
  <meta property="og:description" content="تطبيق أسرة المشعل - منصة تواصل أفراد العائلة">
  <meta property="og:image" content="icons/Icon-512.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="أسرة المشعل - Almashal Family">
  <meta property="twitter:description" content="تطبيق أسرة المشعل - منصة تواصل أفراد العائلة">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="المشعل">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <!-- cropperjs for image cropping on web -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.css" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.min.js"></script>

  <!-- Firebase SDK for Web -->
  <script type="module">
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.20.0/firebase-app.js';
    import { getMessaging } from 'https://www.gstatic.com/firebasejs/10.20.0/firebase-messaging.js';
    import { getAnalytics } from 'https://www.gstatic.com/firebasejs/10.20.0/firebase-analytics.js';

    // Make Firebase available globally for Flutter
    window.firebase = {
      initializeApp,
      getMessaging,
      getAnalytics
    };
  </script>

  <title>أسرة المشعل - Almashal Family</title>
  <link rel="manifest" href="manifest.json">

  <!-- Preloader Styles -->
  <style>
    /* Import Arabic font */
    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

    /* Preloader Container */
    #almashal-preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #fff9f2 0%, #f5f0e8 50%, #fff9f2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      font-family: 'Tajawal', Arial, sans-serif;
      direction: rtl;
      text-align: center;
      transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
    }

    /* Hide preloader */
    #almashal-preloader.hidden {
      opacity: 0;
      visibility: hidden;
    }

    /* Logo Container */
    .preloader-logo {
      width: 120px;
      height: 120px;
      background: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 30px;
      box-shadow: 0 10px 30px rgba(190, 158, 127, 0.3);
      animation: logoFloat 3s ease-in-out infinite;
      position: relative;
      overflow: hidden;
      padding: 20px;
    }

    /* Logo Image */
    .preloader-logo img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    /* Logo Ring Animation */
    .preloader-logo::after {
      content: '';
      position: absolute;
      top: -5px;
      left: -5px;
      right: -5px;
      bottom: -5px;
      border: 3px solid transparent;
      border-top: 3px solid #BE9E7F;
      border-radius: 50%;
      animation: logoSpin 2s linear infinite;
    }

    /* App Title */
    .preloader-title {
      font-size: 32px;
      font-weight: 700;
      color: #BE9E7F;
      margin-bottom: 10px;
      animation: titleFadeIn 1s ease-out 0.5s both;
    }

    /* App Subtitle */
    .preloader-subtitle {
      font-size: 16px;
      font-weight: 400;
      color: #8B5A2B;
      margin-bottom: 40px;
      animation: subtitleFadeIn 1s ease-out 0.8s both;
    }

    /* Loading Text */
    .preloader-loading {
      font-size: 14px;
      font-weight: 500;
      color: #757575;
      margin-bottom: 20px;
      animation: loadingPulse 2s ease-in-out infinite;
    }

    /* Loading Spinner */
    .preloader-spinner {
      width: 40px;
      height: 40px;
      position: relative;
      margin: 20px auto;
    }

    /* Spinner Dots */
    .spinner-dot {
      width: 8px;
      height: 8px;
      background: #BE9E7F;
      border-radius: 50%;
      position: absolute;
      animation: spinnerRotate 1.2s linear infinite;
    }

    .spinner-dot:nth-child(1) {
      top: 0;
      left: 50%;
      margin-left: -4px;
      animation-delay: 0s;
    }

    .spinner-dot:nth-child(2) {
      top: 6px;
      right: 6px;
      animation-delay: -0.1s;
    }

    .spinner-dot:nth-child(3) {
      right: 0;
      top: 50%;
      margin-top: -4px;
      animation-delay: -0.2s;
    }

    .spinner-dot:nth-child(4) {
      bottom: 6px;
      right: 6px;
      animation-delay: -0.3s;
    }

    .spinner-dot:nth-child(5) {
      bottom: 0;
      left: 50%;
      margin-left: -4px;
      animation-delay: -0.4s;
    }

    .spinner-dot:nth-child(6) {
      bottom: 6px;
      left: 6px;
      animation-delay: -0.5s;
    }

    .spinner-dot:nth-child(7) {
      left: 0;
      top: 50%;
      margin-top: -4px;
      animation-delay: -0.6s;
    }

    .spinner-dot:nth-child(8) {
      top: 6px;
      left: 6px;
      animation-delay: -0.7s;
    }

    /* Progress Bar */
    .preloader-progress {
      width: 200px;
      height: 4px;
      background: rgba(190, 158, 127, 0.2);
      border-radius: 2px;
      overflow: hidden;
      margin-top: 20px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #BE9E7F, #8B5A2B);
      border-radius: 2px;
      animation: progressFill 3s ease-in-out infinite;
    }

    /* Animations */
    @keyframes logoFloat {

      0%,
      100% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(-10px);
      }
    }

    @keyframes logoRotate {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes logoSpin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes titleFadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes subtitleFadeIn {
      from {
        opacity: 0;
        transform: translateY(15px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes loadingPulse {

      0%,
      100% {
        opacity: 0.6;
      }

      50% {
        opacity: 1;
      }
    }

    @keyframes spinnerRotate {
      0% {
        opacity: 1;
        transform: scale(1);
      }

      50% {
        opacity: 0.3;
        transform: scale(0.8);
      }

      100% {
        opacity: 1;
        transform: scale(1);
      }
    }

    @keyframes progressFill {
      0% {
        width: 0%;
      }

      50% {
        width: 70%;
      }

      100% {
        width: 100%;
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .preloader-logo {
        width: 100px;
        height: 100px;
      }

      .preloader-logo::before {
        font-size: 40px;
      }

      .preloader-title {
        font-size: 28px;
      }

      .preloader-subtitle {
        font-size: 14px;
      }

      .preloader-progress {
        width: 150px;
      }
    }

    @media (max-width: 480px) {
      .preloader-logo {
        width: 80px;
        height: 80px;
      }

      .preloader-logo::before {
        font-size: 32px;
      }

      .preloader-title {
        font-size: 24px;
      }

      .preloader-subtitle {
        font-size: 12px;
      }
    }
  </style>
</head>

<body>
  <!-- Almashal Family Preloader -->
  <div id="almashal-preloader">
    <!-- Logo -->
    <div class="preloader-logo">
      <img src="logo.svg" alt="أسرة المشعل" onerror="this.src='logo.png'">
    </div>

    <!-- App Title -->
    <div class="preloader-title">أسرة المشعل</div>

    <!-- App Subtitle -->
    <div class="preloader-subtitle">منصة تواصل أفراد العائلة</div>

    <!-- Loading Text -->
    <div class="preloader-loading">جاري التحميل...</div>

    <!-- Loading Spinner -->
    <div class="preloader-spinner">
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
      <div class="spinner-dot"></div>
    </div>

    <!-- Progress Bar -->
    <div class="preloader-progress">
      <div class="progress-fill"></div>
    </div>
  </div>

  <!-- Preloader Script -->
  <script>
    // Preloader management
    (function () {
      'use strict';

      // Configuration
      const PRELOADER_ID = 'almashal-preloader';
      const MIN_DISPLAY_TIME = 1500; // Minimum time to show preloader (ms)
      const FADE_OUT_DELAY = 500; // Delay before fade out (ms)

      let preloaderStartTime = Date.now();
      let flutterLoaded = false;
      let preloaderElement = document.getElementById(PRELOADER_ID);

      // Hide preloader function
      function hidePreloader() {
        if (!preloaderElement) return;

        const elapsedTime = Date.now() - preloaderStartTime;
        const remainingTime = Math.max(0, MIN_DISPLAY_TIME - elapsedTime);

        setTimeout(() => {
          preloaderElement.classList.add('hidden');

          // Remove from DOM after transition
          setTimeout(() => {
            if (preloaderElement && preloaderElement.parentNode) {
              preloaderElement.parentNode.removeChild(preloaderElement);
            }
          }, 800); // Match CSS transition duration
        }, remainingTime + FADE_OUT_DELAY);
      }

      // Listen for Flutter app ready
      function onFlutterReady() {
        flutterLoaded = true;
        hidePreloader();
      }

      // Multiple ways to detect Flutter is ready

      // Method 1: Listen for Flutter engine ready
      window.addEventListener('flutter-first-frame', onFlutterReady);

      // Method 2: Check for Flutter app element
      function checkFlutterApp() {
        const flutterApp = document.querySelector('flutter-view') ||
          document.querySelector('flt-glass-pane') ||
          document.querySelector('[flt-renderer]');

        if (flutterApp && !flutterLoaded) {
          onFlutterReady();
        }
      }

      // Method 3: Fallback timeout
      setTimeout(() => {
        if (!flutterLoaded) {
          console.log('Almashal Preloader: Fallback timeout reached');
          onFlutterReady();
        }
      }, 10000); // 10 seconds fallback

      // Method 4: MutationObserver to watch for Flutter elements
      if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function (mutations) {
          mutations.forEach(function (mutation) {
            if (mutation.type === 'childList') {
              checkFlutterApp();
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }

      // Method 5: Periodic check
      const checkInterval = setInterval(() => {
        checkFlutterApp();
        if (flutterLoaded) {
          clearInterval(checkInterval);
        }
      }, 100);

      // Method 6: Listen for window load as final fallback
      window.addEventListener('load', () => {
        setTimeout(() => {
          if (!flutterLoaded) {
            console.log('Almashal Preloader: Window load fallback');
            onFlutterReady();
          }
        }, 2000);
      });

      // Debug logging
      console.log('Almashal Family Preloader initialized');

      // Expose global function for manual hiding (if needed)
      window.hideAlmashalPreloader = onFlutterReady;

    })();
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>

</html>